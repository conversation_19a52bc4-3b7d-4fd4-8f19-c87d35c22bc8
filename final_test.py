#!/usr/bin/env python3
"""
最终验证修改后的程序
"""

import os
import sys

def test_import():
    """测试导入是否正常"""
    try:
        print("测试导入 extract_utils...")
        sys.path.append('/home/<USER>/archive_pull')
        from extract_utils import extract_file_content_and_meta
        print("✅ 导入成功")
        return extract_file_content_and_meta
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_extraction(extract_func):
    """测试提取功能"""
    test_file = "/data/ready_upload/f9236352168.xlsx"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print(f"\n测试文件: {os.path.basename(test_file)}")
    
    try:
        result = extract_func(test_file)
        text, create_time, creator = result
        
        if text:
            print(f"✅ 提取成功")
            print(f"内容长度: {len(text)} 字符")
            print(f"创建时间: {create_time}")
            print(f"创建者: {creator}")
            print("内容预览:")
            print("-" * 30)
            print(text[:200])
            print("-" * 30)
        else:
            print("❌ 提取失败：无内容")
            
    except Exception as e:
        print(f"❌ 提取异常: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("最终验证修改后的程序")
    print("=" * 40)
    
    # 测试导入
    extract_func = test_import()
    if not extract_func:
        return
    
    # 测试提取
    test_extraction(extract_func)
    
    print("\n验证完成")

if __name__ == "__main__":
    main()
