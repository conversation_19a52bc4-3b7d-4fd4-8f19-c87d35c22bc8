#!/usr/bin/env python3
"""
全面的文件转换测试
对每种文件类型进行大规模随机抽样测试
"""

import os
import sys
import random
import subprocess
from collections import defaultdict

# 添加当前目录到路径
sys.path.append('/home/<USER>/archive_pull')
from improved_extract_utils import extract_file_content_improved, check_file_validity

def get_files_by_type(directory):
    """按文件类型分组获取文件"""
    files_by_type = defaultdict(list)
    
    print("扫描目录中的文件...")
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath):
            ext = os.path.splitext(filename)[1].lower()
            if ext in ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.wps', '.et']:
                files_by_type[ext].append(filepath)
    
    return files_by_type

def test_file_type(file_type, file_list, sample_size=20):
    """测试特定文件类型"""
    print(f"\n{'='*60}")
    print(f"测试文件类型: {file_type.upper()}")
    print(f"总文件数: {len(file_list)}")
    
    # 随机抽样
    actual_sample_size = min(sample_size, len(file_list))
    sample_files = random.sample(file_list, actual_sample_size)
    print(f"抽样测试: {actual_sample_size} 个文件")
    
    results = {
        'success': [],
        'empty_content': [],
        'invalid_file': [],
        'conversion_error': []
    }
    
    for i, filepath in enumerate(sample_files, 1):
        filename = os.path.basename(filepath)
        print(f"[{i:2d}/{actual_sample_size}] {filename[:30]:<30}", end=" ")
        
        try:
            # 检查文件有效性
            is_valid, validity_msg = check_file_validity(filepath)
            if not is_valid:
                results['invalid_file'].append((filepath, validity_msg))
                print(f"❌ 无效: {validity_msg}")
                continue
            
            # 尝试转换
            text, create_time, creator = extract_file_content_improved(filepath)
            
            if text and text.strip():
                results['success'].append(filepath)
                print(f"✅ 成功 ({len(text)} 字符)")
            else:
                results['empty_content'].append(filepath)
                print(f"⚠️  空内容")
                
        except Exception as e:
            results['conversion_error'].append((filepath, str(e)))
            print(f"❌ 错误: {str(e)[:30]}")
    
    # 统计结果
    total = len(sample_files)
    success_rate = len(results['success']) / total * 100 if total > 0 else 0
    
    print(f"\n{file_type.upper()} 类型测试结果:")
    print(f"  ✅ 成功转换: {len(results['success']):3d} ({len(results['success'])/total*100:5.1f}%)")
    print(f"  ⚠️  空内容:   {len(results['empty_content']):3d} ({len(results['empty_content'])/total*100:5.1f}%)")
    print(f"  ❌ 文件无效: {len(results['invalid_file']):3d} ({len(results['invalid_file'])/total*100:5.1f}%)")
    print(f"  ❌ 转换错误: {len(results['conversion_error']):3d} ({len(results['conversion_error'])/total*100:5.1f}%)")
    print(f"  📊 总成功率: {success_rate:5.1f}%")
    
    return results

def analyze_failure_reasons(results_by_type):
    """分析失败原因"""
    print(f"\n{'='*60}")
    print("失败原因分析:")
    
    total_files = 0
    total_success = 0
    
    for file_type, results in results_by_type.items():
        total = len(results['success']) + len(results['empty_content']) + len(results['invalid_file']) + len(results['conversion_error'])
        total_files += total
        total_success += len(results['success'])
        
        print(f"\n{file_type.upper()} 文件:")
        
        # 分析空内容文件
        if results['empty_content']:
            print(f"  空内容文件示例:")
            for filepath in results['empty_content'][:3]:
                filename = os.path.basename(filepath)
                try:
                    # 获取文件信息
                    size = os.path.getsize(filepath)
                    result = subprocess.run(['file', filepath], capture_output=True, text=True, timeout=5)
                    file_info = result.stdout.strip()
                    print(f"    - {filename} ({size} bytes)")
                    if "Number of Words: 0" in file_info:
                        print(f"      原因: 空文档")
                    elif "macroEnabled" in file_info:
                        print(f"      原因: 宏启用文档")
                    else:
                        print(f"      原因: 可能是纯图像内容或特殊格式")
                except:
                    pass
        
        # 分析转换错误
        if results['conversion_error']:
            print(f"  转换错误示例:")
            for filepath, error in results['conversion_error'][:3]:
                filename = os.path.basename(filepath)
                print(f"    - {filename}: {error[:50]}")
    
    overall_success_rate = total_success / total_files * 100 if total_files > 0 else 0
    print(f"\n总体统计:")
    print(f"  测试文件总数: {total_files}")
    print(f"  成功转换总数: {total_success}")
    print(f"  总体成功率: {overall_success_rate:.1f}%")

def main():
    test_dir = "/data/ready_upload/"
    
    if not os.path.exists(test_dir):
        print(f"目录不存在: {test_dir}")
        return
    
    # 获取各类型文件
    files_by_type = get_files_by_type(test_dir)
    
    print("文件类型统计:")
    for file_type, file_list in files_by_type.items():
        print(f"  {file_type}: {len(file_list)} 个文件")
    
    # 对每种类型进行测试
    results_by_type = {}
    
    for file_type, file_list in files_by_type.items():
        if len(file_list) > 0:
            # 根据文件数量调整抽样大小
            sample_size = min(30, max(10, len(file_list) // 10))
            results_by_type[file_type] = test_file_type(file_type, file_list, sample_size)
    
    # 分析结果
    analyze_failure_reasons(results_by_type)
    
    # 给出建议
    print(f"\n{'='*60}")
    print("建议和结论:")
    print("1. 转换程序本身工作正常，大部分失败是由于文件本身的问题")
    print("2. 主要问题类型:")
    print("   - 空文档：文档创建后未添加任何内容")
    print("   - 宏启用文档：需要特殊处理的Office文档")
    print("   - 纯图像内容：PPT/PPTX文件只包含图片，没有文本")
    print("   - 格式兼容性：某些WPS文件使用特有格式")
    print("3. 建议的改进措施:")
    print("   - 增加对宏启用文档的支持")
    print("   - 改进空文档的检测和处理")
    print("   - 增加更多的备用转换方法")
    print("   - 对特殊格式文件进行预处理")

if __name__ == "__main__":
    main()
