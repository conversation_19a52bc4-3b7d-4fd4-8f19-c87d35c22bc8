#!/usr/bin/env python3
"""
改进的文件内容提取工具
处理各种特殊情况和文件格式
"""

import os
import subprocess
import tempfile
from extract_utils import *

def extract_docx_with_fallback(filepath):
    """改进的DOCX提取，支持宏启用文档"""
    try:
        # 首先尝试原始方法
        text, create_time, creator = extract_docx_content_and_meta(filepath)
        if text and text.strip():
            return text, create_time, creator
    except Exception as e:
        print(f"标准DOCX提取失败: {e}")
    
    # 尝试使用libreoffice转换
    try:
        with tempfile.TemporaryDirectory() as tmpdir:
            result = subprocess.run([
                'libreoffice', '--headless', '--convert-to', 'txt:Text',
                '--outdir', tmpdir, filepath
            ], capture_output=True, text=True, timeout=120)
            
            txt_file = os.path.join(tmpdir, os.path.basename(filepath) + '.txt')
            if os.path.exists(txt_file):
                with open(txt_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                if content.strip():
                    return content, None, ""
    except Exception as e:
        print(f"LibreOffice DOCX转换失败: {e}")
    
    return "", None, ""

def extract_pptx_with_fallback(filepath):
    """改进的PPTX提取，处理纯图像幻灯片"""
    try:
        # 首先尝试原始方法
        text, create_time, creator = extract_pptx_content_and_meta(filepath)
        if text and text.strip():
            return text, create_time, creator
    except Exception as e:
        print(f"标准PPTX提取失败: {e}")
    
    # 尝试使用libreoffice转换
    try:
        with tempfile.TemporaryDirectory() as tmpdir:
            result = subprocess.run([
                'libreoffice', '--headless', '--convert-to', 'txt:Text',
                '--outdir', tmpdir, filepath
            ], capture_output=True, text=True, timeout=120)
            
            txt_file = os.path.join(tmpdir, os.path.basename(filepath) + '.txt')
            if os.path.exists(txt_file):
                with open(txt_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                if content.strip():
                    return content, None, ""
    except Exception as e:
        print(f"LibreOffice PPTX转换失败: {e}")
    
    return "", None, ""

def check_file_validity(filepath):
    """检查文件是否有效且包含内容"""
    try:
        # 检查文件大小
        size = os.path.getsize(filepath)
        if size == 0:
            return False, "文件为空"
        
        # 检查文件头
        with open(filepath, 'rb') as f:
            header = f.read(64)
        
        ext = os.path.splitext(filepath)[1].lower()
        
        # 检查Office文件的特征
        if ext in ['.doc', '.wps']:
            # OLE文件头
            if not header.startswith(b'\xd0\xcf\x11\xe0'):
                return False, "不是有效的OLE文件"
        elif ext in ['.docx', '.pptx', '.xlsx']:
            # ZIP文件头
            if not header.startswith(b'PK'):
                return False, "不是有效的ZIP文件"
        
        return True, "文件有效"
        
    except Exception as e:
        return False, f"文件检查失败: {e}"

def extract_file_content_improved(filepath):
    """改进的文件内容提取函数"""
    # 首先检查文件有效性
    is_valid, msg = check_file_validity(filepath)
    if not is_valid:
        print(f"文件无效: {msg}")
        return "", None, ""
    
    ext = os.path.splitext(filepath)[1].lower()
    text, create_time, creator = "", None, ""
    
    try:
        if ext == ".docx":
            text, create_time, creator = extract_docx_with_fallback(filepath)
        elif ext == ".pptx":
            text, create_time, creator = extract_pptx_with_fallback(filepath)
        elif ext == ".doc":
            # 对于DOC文件，先检查是否为空文档
            try:
                result = subprocess.run(['file', filepath], capture_output=True, text=True, timeout=10)
                if "Number of Words: 0" in result.stdout:
                    print("检测到空DOC文档")
                    return "", None, ""
            except:
                pass
            
            # 使用原始方法
            text = extract_doc_content_antiword(filepath)
            if not text:
                text = extract_doc_content_libreoffice(filepath)
        elif ext == ".wps":
            # 对于WPS文件，使用多种方法
            text = extract_wps_content_multimethod(filepath)
        else:
            # 其他格式使用原始方法
            text, create_time, creator = extract_file_content_and_meta(filepath)
        
        # 检查提取结果
        if text and text.strip():
            text = truncate_text(text)
            return text, create_time, creator
        else:
            print(f"未能从文件中提取到文本内容: {filepath}")
            return "", create_time, creator
            
    except Exception as e:
        print(f"文件内容提取异常: {e}")
        return "", None, ""

def batch_test_files(file_list, max_files=10):
    """批量测试文件转换"""
    print(f"批量测试 {min(len(file_list), max_files)} 个文件...")
    
    results = {
        'success': [],
        'empty': [],
        'invalid': [],
        'error': []
    }
    
    test_files = file_list[:max_files]
    
    for i, filepath in enumerate(test_files, 1):
        print(f"\n[{i}/{len(test_files)}] 测试: {os.path.basename(filepath)}")
        
        try:
            text, create_time, creator = extract_file_content_improved(filepath)
            
            if text and text.strip():
                results['success'].append(filepath)
                print(f"✅ 成功 - 文本长度: {len(text)} 字符")
            else:
                # 检查是否为空文档
                is_valid, msg = check_file_validity(filepath)
                if is_valid:
                    results['empty'].append(filepath)
                    print(f"⚠️  空内容 - {msg}")
                else:
                    results['invalid'].append(filepath)
                    print(f"❌ 无效 - {msg}")
                    
        except Exception as e:
            results['error'].append(filepath)
            print(f"❌ 错误 - {e}")
    
    # 输出统计
    print(f"\n{'='*60}")
    print("批量测试结果统计:")
    print(f"✅ 成功转换: {len(results['success'])} 个")
    print(f"⚠️  空内容: {len(results['empty'])} 个")
    print(f"❌ 文件无效: {len(results['invalid'])} 个")
    print(f"❌ 转换错误: {len(results['error'])} 个")
    print(f"总成功率: {len(results['success'])/len(test_files)*100:.1f}%")
    
    return results

if __name__ == "__main__":
    # 测试一些文件
    test_files = [
        "/data/ready_upload/f3019234512_1.doc",
        "/data/ready_upload/f4988655456_1.docx", 
        "/data/ready_upload/f8769543088_1.pptx",
        "/data/ready_upload/f8261965536_1.wps",
        "/data/ready_upload/f10203409896.xls",
        "/data/ready_upload/f9236352168.xlsx"
    ]
    
    batch_test_files(test_files)
