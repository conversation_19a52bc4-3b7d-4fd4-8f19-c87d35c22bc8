#!/usr/bin/env python3
"""
测试文件转换功能
随机选择几个不同格式的文件进行转换测试
"""

import os
import sys
import random
import traceback
from extract_utils import extract_file_content_and_meta

def test_file_conversion(filepath):
    """测试单个文件的转换"""
    print(f"\n{'='*60}")
    print(f"测试文件: {os.path.basename(filepath)}")
    print(f"完整路径: {filepath}")
    print(f"文件大小: {os.path.getsize(filepath) / (1024*1024):.2f} MB")
    print(f"文件扩展名: {os.path.splitext(filepath)[1].lower()}")

    # 检查文件类型
    import subprocess
    try:
        result = subprocess.run(['file', filepath], capture_output=True, text=True, timeout=10)
        print(f"文件类型: {result.stdout.strip()}")
    except:
        pass

    # 检查文件是否存在
    if not os.path.exists(filepath):
        print("❌ 文件不存在")
        return False

    # 检查文件是否可读
    try:
        with open(filepath, 'rb') as f:
            header = f.read(1024)  # 尝试读取前1KB
        print("✅ 文件可读")
        print(f"文件头部（前32字节）: {header[:32].hex()}")
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        return False

    # 检查是否为空文件或损坏文件
    if os.path.getsize(filepath) == 0:
        print("❌ 文件为空")
        return False

    # 尝试转换
    try:
        print("开始转换...")
        text, create_time, creator = extract_file_content_and_meta(filepath)

        if text and text.strip():
            print(f"✅ 转换成功")
            print(f"提取文本长度: {len(text)} 字符")
            print(f"创建时间: {create_time}")
            print(f"创建者: {creator}")
            print("文本预览（前200字符）:")
            print("-" * 40)
            print(text[:200] + ("..." if len(text) > 200 else ""))
            print("-" * 40)
            return True
        else:
            print("❌ 转换失败: 未提取到文本内容")
            # 尝试手动测试各种转换方法
            test_manual_conversion(filepath)
            return False

    except Exception as e:
        print(f"❌ 转换异常: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def test_manual_conversion(filepath):
    """手动测试各种转换方法"""
    ext = os.path.splitext(filepath)[1].lower()
    print(f"\n手动测试 {ext} 文件的各种转换方法:")

    if ext == '.doc':
        # 测试 antiword
        try:
            result = subprocess.run(['antiword', filepath], capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                print(f"✅ antiword 成功: {len(result.stdout)} 字符")
            else:
                print(f"❌ antiword 失败: {result.stderr}")
        except Exception as e:
            print(f"❌ antiword 异常: {e}")

        # 测试 libreoffice
        try:
            import tempfile
            with tempfile.TemporaryDirectory() as tmpdir:
                result = subprocess.run([
                    'libreoffice', '--headless', '--convert-to', 'txt:Text',
                    '--outdir', tmpdir, filepath
                ], capture_output=True, text=True, timeout=60)

                txt_file = os.path.join(tmpdir, os.path.basename(filepath) + '.txt')
                if os.path.exists(txt_file):
                    with open(txt_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    if content.strip():
                        print(f"✅ libreoffice 成功: {len(content)} 字符")
                    else:
                        print("❌ libreoffice 转换后文件为空")
                else:
                    print(f"❌ libreoffice 失败: {result.stderr}")
        except Exception as e:
            print(f"❌ libreoffice 异常: {e}")

    elif ext in ['.docx', '.pptx']:
        # 测试 python-docx/python-pptx
        if ext == '.docx':
            try:
                from docx import Document
                doc = Document(filepath)
                text = "\n".join([p.text for p in doc.paragraphs])
                if text.strip():
                    print(f"✅ python-docx 成功: {len(text)} 字符")
                else:
                    print("❌ python-docx 提取文本为空")
            except Exception as e:
                print(f"❌ python-docx 异常: {e}")

        elif ext == '.pptx':
            try:
                from pptx import Presentation
                prs = Presentation(filepath)
                text = []
                for slide in prs.slides:
                    for shape in slide.shapes:
                        if hasattr(shape, "text"):
                            text.append(shape.text)
                content = "\n".join(text)
                if content.strip():
                    print(f"✅ python-pptx 成功: {len(content)} 字符")
                else:
                    print("❌ python-pptx 提取文本为空")
            except Exception as e:
                print(f"❌ python-pptx 异常: {e}")

    elif ext in ['.xls', '.xlsx']:
        # 测试 openpyxl/xlrd
        if ext == '.xlsx':
            try:
                import openpyxl
                wb = openpyxl.load_workbook(filepath, read_only=True)
                text = []
                for ws in wb.worksheets:
                    for row in ws.iter_rows(values_only=True):
                        text.append("\t".join([str(cell) if cell is not None else "" for cell in row]))
                content = "\n".join(text)
                if content.strip():
                    print(f"✅ openpyxl 成功: {len(content)} 字符")
                else:
                    print("❌ openpyxl 提取文本为空")
            except Exception as e:
                print(f"❌ openpyxl 异常: {e}")

        elif ext == '.xls':
            try:
                import xlrd
                wb = xlrd.open_workbook(filepath)
                text_rows = []
                for sheet in wb.sheets():
                    for row_idx in range(sheet.nrows):
                        row = sheet.row_values(row_idx)
                        text_rows.append("\t".join([str(cell) for cell in row]))
                content = "\n".join(text_rows)
                if content.strip():
                    print(f"✅ xlrd 成功: {len(content)} 字符")
                else:
                    print("❌ xlrd 提取文本为空")
            except Exception as e:
                print(f"❌ xlrd 异常: {e}")

    elif ext == '.ppt':
        # 测试 catppt
        try:
            result = subprocess.run(['catppt', filepath], capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                print(f"✅ catppt 成功: {len(result.stdout)} 字符")
            else:
                print(f"❌ catppt 失败: {result.stderr}")
        except Exception as e:
            print(f"❌ catppt 异常: {e}")

def main():
    test_dir = "/data/ready_upload/"

    if not os.path.exists(test_dir):
        print(f"目录不存在: {test_dir}")
        return

    # 获取所有支持的文件格式
    supported_extensions = ['.doc', '.docx', '.xlsx', '.xls', '.et', '.ppt', '.pptx', '.wps']

    print("扫描目录中的文件...")
    all_files = []
    for filename in os.listdir(test_dir):
        filepath = os.path.join(test_dir, filename)
        if os.path.isfile(filepath):
            ext = os.path.splitext(filename)[1].lower()
            if ext in supported_extensions:
                all_files.append((filepath, ext))

    print(f"找到 {len(all_files)} 个支持的文件")

    # 按文件类型分组
    files_by_type = {}
    for filepath, ext in all_files:
        if ext not in files_by_type:
            files_by_type[ext] = []
        files_by_type[ext].append(filepath)

    print("文件类型统计:")
    for ext, files in files_by_type.items():
        print(f"  {ext}: {len(files)} 个文件")

    # 从每种类型中随机选择1-2个文件进行测试
    test_files = []
    for ext, files in files_by_type.items():
        if len(files) > 0:
            # 每种类型最多选择2个文件
            sample_count = min(2, len(files))
            selected = random.sample(files, sample_count)
            test_files.extend(selected)

    print(f"\n随机选择了 {len(test_files)} 个文件进行测试:")
    for filepath in test_files:
        print(f"  - {os.path.basename(filepath)}")

    # 开始测试
    print(f"\n{'='*60}")
    print("开始转换测试")
    print(f"{'='*60}")

    success_count = 0
    total_count = len(test_files)

    for i, filepath in enumerate(test_files, 1):
        print(f"\n[{i}/{total_count}]", end=" ")
        if test_file_conversion(filepath):
            success_count += 1

    # 总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    print(f"总测试文件数: {total_count}")
    print(f"转换成功: {success_count}")
    print(f"转换失败: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")

    if success_count < total_count:
        print("\n❌ 存在转换失败的文件，可能的原因:")
        print("1. 文件损坏或格式不标准")
        print("2. 转换程序缺少必要的依赖")
        print("3. 文件过大导致转换超时")
        print("4. 文件使用了不支持的编码或特殊格式")
    else:
        print("\n✅ 所有测试文件转换成功！")

if __name__ == "__main__":
    main()
