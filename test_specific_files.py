#!/usr/bin/env python3
"""
测试特定失败文件的转换
"""

import os
import sys
import subprocess
import traceback

# 添加当前目录到路径
sys.path.append('/home/<USER>/archive_pull')
from extract_utils import extract_file_content_and_meta

def test_specific_file(filepath):
    """详细测试特定文件"""
    print(f"\n{'='*80}")
    print(f"详细测试文件: {os.path.basename(filepath)}")
    print(f"完整路径: {filepath}")
    
    if not os.path.exists(filepath):
        print("❌ 文件不存在")
        return
    
    # 基本信息
    size = os.path.getsize(filepath)
    print(f"文件大小: {size} 字节 ({size/(1024*1024):.2f} MB)")
    
    # 文件类型检测
    try:
        result = subprocess.run(['file', filepath], capture_output=True, text=True, timeout=10)
        print(f"文件类型: {result.stdout.strip()}")
    except Exception as e:
        print(f"文件类型检测失败: {e}")
    
    # 文件头部
    try:
        with open(filepath, 'rb') as f:
            header = f.read(64)
        print(f"文件头部（前64字节）: {header.hex()}")
        print(f"文件头部（ASCII）: {header[:32]}")
    except Exception as e:
        print(f"读取文件头部失败: {e}")
    
    ext = os.path.splitext(filepath)[1].lower()
    print(f"文件扩展名: {ext}")
    
    # 测试原始转换函数
    print(f"\n--- 测试原始转换函数 ---")
    try:
        text, create_time, creator = extract_file_content_and_meta(filepath)
        if text and text.strip():
            print(f"✅ 原始转换成功: {len(text)} 字符")
            print(f"文本预览: {text[:100]}...")
        else:
            print("❌ 原始转换失败: 无文本内容")
    except Exception as e:
        print(f"❌ 原始转换异常: {e}")
        traceback.print_exc()
    
    # 根据文件类型进行详细测试
    if ext == '.doc':
        test_doc_file(filepath)
    elif ext == '.docx':
        test_docx_file(filepath)
    elif ext == '.pptx':
        test_pptx_file(filepath)
    elif ext == '.wps':
        test_wps_file(filepath)

def test_doc_file(filepath):
    """测试DOC文件"""
    print(f"\n--- 测试DOC文件转换方法 ---")
    
    # 1. antiword
    print("1. 测试 antiword:")
    try:
        result = subprocess.run(['antiword', filepath], capture_output=True, text=True, timeout=30)
        print(f"   返回码: {result.returncode}")
        if result.stdout:
            print(f"   输出长度: {len(result.stdout)} 字符")
            print(f"   输出预览: {result.stdout[:100]}...")
        if result.stderr:
            print(f"   错误信息: {result.stderr}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 2. libreoffice
    print("2. 测试 libreoffice:")
    try:
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            result = subprocess.run([
                'libreoffice', '--headless', '--convert-to', 'txt:Text',
                '--outdir', tmpdir, filepath
            ], capture_output=True, text=True, timeout=120)
            
            print(f"   返回码: {result.returncode}")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
            
            # 检查输出文件
            expected_txt = os.path.join(tmpdir, os.path.basename(filepath) + '.txt')
            if os.path.exists(expected_txt):
                with open(expected_txt, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                print(f"   转换成功，文本长度: {len(content)} 字符")
                if content.strip():
                    print(f"   文本预览: {content[:100]}...")
                else:
                    print("   转换后文件为空")
            else:
                print("   转换后文件不存在")
                print(f"   临时目录内容: {os.listdir(tmpdir)}")
    except Exception as e:
        print(f"   异常: {e}")

def test_docx_file(filepath):
    """测试DOCX文件"""
    print(f"\n--- 测试DOCX文件转换方法 ---")
    
    # 1. python-docx
    print("1. 测试 python-docx:")
    try:
        from docx import Document
        doc = Document(filepath)
        
        # 获取段落文本
        paragraphs = [p.text for p in doc.paragraphs]
        text = "\n".join(paragraphs)
        
        print(f"   段落数量: {len(paragraphs)}")
        print(f"   文本长度: {len(text)} 字符")
        
        if text.strip():
            print(f"   文本预览: {text[:100]}...")
        else:
            print("   提取的文本为空")
            
        # 检查文档属性
        try:
            props = doc.core_properties
            print(f"   创建时间: {props.created}")
            print(f"   作者: {props.author}")
        except:
            pass
            
    except Exception as e:
        print(f"   异常: {e}")
        traceback.print_exc()

def test_pptx_file(filepath):
    """测试PPTX文件"""
    print(f"\n--- 测试PPTX文件转换方法 ---")
    
    # 1. python-pptx
    print("1. 测试 python-pptx:")
    try:
        from pptx import Presentation
        prs = Presentation(filepath)
        
        print(f"   幻灯片数量: {len(prs.slides)}")
        
        text_parts = []
        for i, slide in enumerate(prs.slides):
            slide_text = []
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text:
                    slide_text.append(shape.text)
            if slide_text:
                text_parts.extend(slide_text)
                print(f"   幻灯片 {i+1}: {len(slide_text)} 个文本框")
        
        text = "\n".join(text_parts)
        print(f"   总文本长度: {len(text)} 字符")
        
        if text.strip():
            print(f"   文本预览: {text[:100]}...")
        else:
            print("   提取的文本为空")
            
    except Exception as e:
        print(f"   异常: {e}")
        traceback.print_exc()

def test_wps_file(filepath):
    """测试WPS文件"""
    print(f"\n--- 测试WPS文件转换方法 ---")
    
    # 1. antiword
    print("1. 测试 antiword:")
    try:
        result = subprocess.run(['antiword', filepath], capture_output=True, text=True, timeout=30)
        print(f"   返回码: {result.returncode}")
        if result.stdout:
            print(f"   输出长度: {len(result.stdout)} 字符")
            print(f"   输出预览: {result.stdout[:100]}...")
        if result.stderr:
            print(f"   错误信息: {result.stderr}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 2. libreoffice 转 txt
    print("2. 测试 libreoffice 转 txt:")
    try:
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            result = subprocess.run([
                'libreoffice', '--headless', '--convert-to', 'txt:Text',
                '--outdir', tmpdir, filepath
            ], capture_output=True, text=True, timeout=120)
            
            print(f"   返回码: {result.returncode}")
            if result.stderr:
                print(f"   错误信息: {result.stderr}")
            
            expected_txt = os.path.join(tmpdir, os.path.basename(filepath) + '.txt')
            if os.path.exists(expected_txt):
                with open(expected_txt, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                print(f"   转换成功，文本长度: {len(content)} 字符")
                if content.strip():
                    print(f"   文本预览: {content[:100]}...")
                else:
                    print("   转换后文件为空")
            else:
                print("   转换后文件不存在")
    except Exception as e:
        print(f"   异常: {e}")

def main():
    # 测试几个失败的文件
    test_files = [
        "/data/ready_upload/f3019234512_1.doc",
        "/data/ready_upload/f4988655456_1.docx", 
        "/data/ready_upload/f8769543088_1.pptx",
        "/data/ready_upload/f8261965536_1.wps"
    ]
    
    for filepath in test_files:
        test_specific_file(filepath)

if __name__ == "__main__":
    main()
