#!/usr/bin/env python3
"""
测试改进后的文件处理
特别是对无法提取文本的文件的智能描述
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('/home/<USER>/archive_pull')

def test_file_analysis():
    """测试文件分析功能"""
    # 测试一些可能无法提取文本的文件
    test_files = [
        "/data/ready_upload/f3019234512_1.doc",      # 之前测试失败的DOC文件
        "/data/ready_upload/f4988655456_1.docx",     # 宏启用的DOCX文件
        "/data/ready_upload/f8769543088_1.pptx",     # 大的PPTX文件
        "/data/ready_upload/f8261965536_1.wps",      # WPS文件
        "/data/ready_upload/f2775720160_1.pptx",     # 有文本的PPTX文件（对比）
        "/data/ready_upload/f9236352168.xlsx",       # XLSX文件（对比）
    ]
    
    print("测试改进后的文件处理")
    print("=" * 60)
    
    for i, filepath in enumerate(test_files, 1):
        if not os.path.exists(filepath):
            print(f"[{i}] 文件不存在: {os.path.basename(filepath)}")
            continue
            
        print(f"\n[{i}] 测试文件: {os.path.basename(filepath)}")
        print(f"文件大小: {os.path.getsize(filepath)} 字节 ({os.path.getsize(filepath)//1024}KB)")
        
        try:
            # 测试新的提取函数
            from extract_utils import extract_file_content_and_meta
            text, create_time, creator = extract_file_content_and_meta(filepath)
            
            print(f"✅ 处理成功")
            print(f"内容长度: {len(text)} 字符")
            print(f"创建时间: {create_time}")
            print(f"创建者: {creator}")
            
            # 检查是否是智能描述
            if any(keyword in text for keyword in ["包含图像", "包含图表", "多媒体内容", "特殊格式", "损坏", "空文档"]):
                print("🔍 智能分析结果:")
            else:
                print("📄 提取的文本内容:")
            
            print("-" * 40)
            if len(text) > 200:
                print(text[:200] + "...")
            else:
                print(text)
            print("-" * 40)
                
        except Exception as e:
            print(f"❌ 处理异常: {e}")
            import traceback
            traceback.print_exc()

def test_specific_analysis():
    """测试特定文件的分析功能"""
    print(f"\n{'='*60}")
    print("测试文件类型分析功能")
    print("=" * 60)
    
    # 直接测试分析函数
    test_file = "/data/ready_upload/f3019234512_1.doc"
    
    if not os.path.exists(test_file):
        print("测试文件不存在")
        return
    
    print(f"分析文件: {os.path.basename(test_file)}")
    
    try:
        from extract_utils import analyze_file_content_type
        description = analyze_file_content_type(test_file)
        print(f"分析结果: {description}")
        
        # 同时测试file命令的输出
        import subprocess
        result = subprocess.run(['file', test_file], capture_output=True, text=True, timeout=10)
        print(f"file命令输出: {result.stdout.strip()}")
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == "__main__":
    test_file_analysis()
    test_specific_analysis()
