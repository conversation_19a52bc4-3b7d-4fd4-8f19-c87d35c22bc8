#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试WPS Office转换功能
"""

import os
import subprocess
import tempfile
import time

def test_wps_direct():
    """直接测试WPS Office的转换功能"""
    print("测试WPS Office直接转换功能...")
    
    # 检查WPS安装路径
    wps_path = "/opt/kingsoft/wps-office/office6/wps"
    et_path = "/opt/kingsoft/wps-office/office6/et"
    wpp_path = "/opt/kingsoft/wps-office/office6/wpp"
    
    print(f"WPS路径: {wps_path}, 存在: {os.path.exists(wps_path)}")
    print(f"ET路径: {et_path}, 存在: {os.path.exists(et_path)}")
    print(f"WPP路径: {wpp_path}, 存在: {os.path.exists(wpp_path)}")
    
    # 尝试不同的转换方法
    test_methods = [
        # 方法1: 尝试类似LibreOffice的参数
        {
            'name': 'WPS --headless --convert-to txt',
            'cmd': [wps_path, '--headless', '--convert-to', 'txt']
        },
        # 方法2: 尝试其他可能的参数
        {
            'name': 'WPS --batch --convert txt',
            'cmd': [wps_path, '--batch', '--convert', 'txt']
        },
        # 方法3: 尝试帮助信息
        {
            'name': 'WPS --help',
            'cmd': [wps_path, '--help']
        },
        # 方法4: 尝试版本信息
        {
            'name': 'WPS --version',
            'cmd': [wps_path, '--version']
        }
    ]
    
    results = []
    
    with tempfile.TemporaryDirectory() as tmpdir:
        # 创建测试文档
        test_doc = os.path.join(tmpdir, "test.txt")
        with open(test_doc, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文档\n用于验证WPS转换功能\n")
        
        for method in test_methods:
            print(f"\n测试: {method['name']}")
            
            try:
                if 'convert' in method['name'].lower():
                    # 对于转换命令，添加输出目录和文件
                    cmd = method['cmd'] + ['--outdir', tmpdir, test_doc]
                else:
                    # 对于帮助和版本命令，不添加文件
                    cmd = method['cmd']
                
                print(f"执行命令: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                print(f"返回码: {result.returncode}")
                if result.stdout:
                    print(f"标准输出: {result.stdout[:200]}...")
                if result.stderr:
                    print(f"标准错误: {result.stderr[:200]}...")
                
                results.append({
                    'method': method['name'],
                    'return_code': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                })
                
            except subprocess.TimeoutExpired:
                print("命令超时")
                results.append({
                    'method': method['name'],
                    'error': 'timeout'
                })
            except Exception as e:
                print(f"执行失败: {e}")
                results.append({
                    'method': method['name'],
                    'error': str(e)
                })
    
    # 总结结果
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    for result in results:
        print(f"\n方法: {result['method']}")
        if 'error' in result:
            print(f"  错误: {result['error']}")
        else:
            print(f"  返回码: {result['return_code']}")
            if result.get('stdout'):
                print(f"  输出: {result['stdout'][:100]}...")
            if result.get('stderr'):
                print(f"  错误: {result['stderr'][:100]}...")
    
    return results

if __name__ == "__main__":
    test_wps_direct()
