#!/usr/bin/env python3
"""
简单测试新的提取功能
避免循环导入问题
"""

import os
import sys
import subprocess
import tempfile
from docx import Document as DocxDocument
from pptx import Presentation as PptxPresentation
import openpyxl

def extract_table_content(table):
    """提取表格内容"""
    try:
        table_text = []
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip() if cell.text else ""
                row_text.append(cell_text)
            table_text.append("\t".join(row_text))
        return "\n".join(table_text)
    except:
        return ""

def simple_extract_pptx(filepath):
    """简单的PPTX提取，包含图像占位符"""
    try:
        prs = PptxPresentation(filepath)
        content_parts = []
        
        for slide_idx, slide in enumerate(prs.slides, 1):
            slide_content = []
            slide_content.append(f"=== 幻灯片 {slide_idx} ===")
            
            # 按照形状在幻灯片中的位置排序
            shapes_with_pos = []
            for shape in slide.shapes:
                try:
                    top = shape.top if hasattr(shape, 'top') else 0
                    left = shape.left if hasattr(shape, 'left') else 0
                    shapes_with_pos.append((top, left, shape))
                except:
                    shapes_with_pos.append((0, 0, shape))
            
            # 按位置排序
            shapes_with_pos.sort(key=lambda x: (x[0], x[1]))
            
            for _, _, shape in shapes_with_pos:
                if hasattr(shape, "text") and shape.text.strip():
                    slide_content.append(shape.text.strip())
                elif hasattr(shape, "shape_type"):
                    if shape.shape_type == 13:  # 图片
                        slide_content.append("[图像]")
                    elif shape.shape_type == 3:  # 图表
                        slide_content.append("[图表]")
                    elif shape.shape_type == 19:  # 表格
                        try:
                            if hasattr(shape, 'table'):
                                table_content = extract_table_content(shape.table)
                                if table_content:
                                    slide_content.append(f"[表格]\n{table_content}")
                                else:
                                    slide_content.append("[表格]")
                            else:
                                slide_content.append("[表格]")
                        except:
                            slide_content.append("[表格]")
                    elif shape.shape_type == 6:  # 文本框
                        if hasattr(shape, "text") and shape.text.strip():
                            slide_content.append(shape.text.strip())
                    else:
                        slide_content.append(f"[形状:{shape.shape_type}]")
            
            if len(slide_content) > 1:
                content_parts.append("\n".join(slide_content))
        
        return "\n\n".join(content_parts) if content_parts else ""
    except Exception as e:
        print(f"PPTX处理异常: {e}")
        return ""

def simple_extract_docx(filepath):
    """简单的DOCX提取，包含图像占位符"""
    try:
        doc = DocxDocument(filepath)
        content_parts = []
        
        for para in doc.paragraphs:
            para_content = []
            
            for run in para.runs:
                if run._element.xpath('.//a:blip'):
                    para_content.append("[图像]")
                elif run.text.strip():
                    para_content.append(run.text)
            
            if para_content:
                content_parts.append("".join(para_content))
            elif para.text.strip():
                content_parts.append(para.text)
        
        # 处理表格
        for table in doc.tables:
            table_content = extract_docx_table_content(table)
            if table_content:
                content_parts.append(f"[表格]\n{table_content}")
            else:
                content_parts.append("[表格]")
        
        return "\n".join(content_parts)
    except Exception as e:
        print(f"DOCX处理异常: {e}")
        return ""

def extract_docx_table_content(table):
    """提取DOCX表格内容"""
    try:
        table_text = []
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip() if cell.text else ""
                row_text.append(cell_text)
            table_text.append("\t".join(row_text))
        return "\n".join(table_text)
    except:
        return ""

def simple_extract_xlsx(filepath):
    """简单的XLSX提取，包含图像占位符"""
    try:
        wb = openpyxl.load_workbook(filepath, read_only=True)
        content_parts = []
        
        for ws_idx, ws in enumerate(wb.worksheets, 1):
            sheet_content = []
            sheet_content.append(f"=== 工作表 {ws_idx}: {ws.title} ===")
            
            has_data = False
            for row in ws.iter_rows(values_only=True):
                row_data = [str(cell) if cell is not None else "" for cell in row]
                if any(cell.strip() for cell in row_data):
                    sheet_content.append("\t".join(row_data))
                    has_data = True
            
            # 简单的图像检测
            try:
                if hasattr(ws, '_charts') and ws._charts:
                    for chart in ws._charts:
                        sheet_content.append("[图表]")
                
                if hasattr(ws, '_images') and ws._images:
                    for image in ws._images:
                        sheet_content.append("[图像]")
            except:
                pass
            
            if has_data or len(sheet_content) > 1:
                content_parts.append("\n".join(sheet_content))
        
        return "\n\n".join(content_parts) if content_parts else ""
    except Exception as e:
        print(f"XLSX处理异常: {e}")
        return ""

def test_files():
    """测试文件"""
    test_files = [
        ("/data/ready_upload/f2775720160_1.pptx", "PPTX"),
        ("/data/ready_upload/f1820358824_1.pptx", "PPTX"),
        ("/data/ready_upload/f9236352168.xlsx", "XLSX"),
    ]
    
    print("测试增强的文本提取功能")
    print("=" * 60)
    
    for filepath, file_type in test_files:
        if not os.path.exists(filepath):
            print(f"文件不存在: {os.path.basename(filepath)}")
            continue
            
        print(f"\n测试 {file_type} 文件: {os.path.basename(filepath)}")
        print(f"文件大小: {os.path.getsize(filepath) / (1024*1024):.2f} MB")
        print("-" * 40)
        
        try:
            if file_type == "PPTX":
                content = simple_extract_pptx(filepath)
            elif file_type == "DOCX":
                content = simple_extract_docx(filepath)
            elif file_type == "XLSX":
                content = simple_extract_xlsx(filepath)
            else:
                content = ""
            
            if content:
                print(f"✅ 提取成功，长度: {len(content)} 字符")
                
                # 统计占位符
                image_count = content.count("[图像]")
                chart_count = content.count("[图表]")
                table_count = content.count("[表格]")
                
                if image_count > 0 or chart_count > 0 or table_count > 0:
                    print(f"🖼️  包含: 图像{image_count}个, 图表{chart_count}个, 表格{table_count}个")
                
                print("内容预览:")
                print("=" * 30)
                preview = content[:400]
                if len(content) > 400:
                    preview += "..."
                print(preview)
                print("=" * 30)
            else:
                print("❌ 提取失败")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_files()
