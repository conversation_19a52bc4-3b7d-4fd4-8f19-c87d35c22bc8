# 依赖库安装说明:
# pip install pyodbc tqdm python-docx python-pptx openpyxl xlrd

import argparse
import json
import os
import sys
import uuid
import hashlib
import shutil
import pyodbc
import datetime
import time
import psutil
import threading
import queue
from tqdm import tqdm
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from contextlib import contextmanager

# 全局资源管理
class ResourceManager:
    def __init__(self, max_memory_percent=80, max_office_processes=2):
        self.max_memory_percent = max_memory_percent
        self.max_office_processes = max_office_processes
        self.office_semaphore = threading.Semaphore(max_office_processes)
        self.db_connections = queue.Queue()
        self.process_monitor = None
        self.office_process_count = 0
        self.office_lock = threading.Lock()
        self.last_cleanup = time.time()

    def init_db_pool(self, conn_params, pool_size=10):
        """初始化数据库连接池"""
        for _ in range(pool_size):
            try:
                conn = pyodbc.connect(conn_params, timeout=10)
                self.db_connections.put(conn)
            except Exception as e:
                print(f"创建数据库连接失败: {e}")

    def get_db_connection(self):
        """获取数据库连接"""
        try:
            return self.db_connections.get(timeout=30)
        except queue.Empty:
            raise Exception("无法获取数据库连接")

    def return_db_connection(self, conn):
        """归还数据库连接"""
        self.db_connections.put(conn)

    @contextmanager
    def office_process_limit(self):
        """限制libreoffice进程数，优化超时和清理策略"""
        # 在获取信号量前先清理僵尸进程
        current_time = time.time()
        if current_time - self.last_cleanup > 30:  # 每30秒清理一次
            self.cleanup_zombie_processes()
            self.last_cleanup = current_time

        # 动态调整超时时间：如果有很多等待的线程，减少超时时间
        with self.office_lock:
            waiting_threads = self.max_office_processes - self.office_semaphore._value
            if waiting_threads > 2:
                timeout = 120  # 2分钟
            elif waiting_threads > 0:
                timeout = 180  # 3分钟
            else:
                timeout = 300  # 5分钟

        print(f"等待LibreOffice进程槽位... (等待线程数: {waiting_threads}, 超时: {timeout}秒)")
        acquired = self.office_semaphore.acquire(timeout=timeout)
        if not acquired:
            # 强制清理并重试一次
            print("LibreOffice进程超时，强制清理进程...")
            self.force_cleanup_office_processes()
            acquired = self.office_semaphore.acquire(timeout=60)
            if not acquired:
                raise Exception("等待libreoffice进程超时")

        try:
            with self.office_lock:
                self.office_process_count += 1
            print(f"获得LibreOffice进程槽位 (当前活跃: {self.office_process_count})")
            yield
        finally:
            with self.office_lock:
                self.office_process_count -= 1
            self.office_semaphore.release()
            print(f"释放LibreOffice进程槽位 (当前活跃: {self.office_process_count})")

    def check_memory(self):
        """检查内存使用情况"""
        memory = psutil.virtual_memory()
        return memory.percent < self.max_memory_percent

    def cleanup_zombie_processes(self):
        """清理僵尸进程"""
        cleaned = 0
        for proc in psutil.process_iter(['name', 'status', 'pid']):
            try:
                if 'soffice' in proc.info['name']:
                    if proc.info['status'] == 'zombie':
                        proc.kill()
                        cleaned += 1
                        print(f"清理僵尸进程: {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        if cleaned > 0:
            print(f"清理了 {cleaned} 个僵尸LibreOffice进程")

    def force_cleanup_office_processes(self):
        """强制清理所有LibreOffice进程"""
        killed = 0
        for proc in psutil.process_iter(['name', 'pid', 'create_time']):
            try:
                if 'soffice' in proc.info['name']:
                    # 杀死运行超过10分钟的LibreOffice进程
                    if time.time() - proc.info['create_time'] > 600:
                        proc.kill()
                        killed += 1
                        print(f"强制杀死长时间运行的LibreOffice进程: {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        if killed > 0:
            print(f"强制清理了 {killed} 个LibreOffice进程")
            time.sleep(2)  # 等待进程完全退出

    def start_monitoring(self):
        """启动资源监控"""
        def monitor():
            while True:
                self.cleanup_zombie_processes()
                if not self.check_memory():
                    print("警告：内存使用率过高")

                # 检查LibreOffice进程数量
                office_count = 0
                for proc in psutil.process_iter(['name']):
                    try:
                        if 'soffice' in proc.info['name']:
                            office_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                if office_count > self.max_office_processes * 2:
                    print(f"警告：LibreOffice进程过多 ({office_count}个)，执行清理...")
                    self.force_cleanup_office_processes()

                time.sleep(30)  # 更频繁的监控

        self.process_monitor = threading.Thread(target=monitor, daemon=True)
        self.process_monitor.start()

# 全局资源管理器
resource_mgr = ResourceManager()

# 读取配置
def load_config(config_path):
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

# 数据库连接
def get_db_conn(cfg):
    conn_str = (
        f"DRIVER={cfg['driver']};"
        f"SERVER={cfg['server']};"
        f"DATABASE={cfg['database']};"
        f"UID={cfg['uid']};"
        f"PWD={cfg['pwd']};"
        f"Encrypt={cfg.get('encrypt', 'yes')};"
        f"TrustServerCertificate={cfg.get('trustServerCertificate', 'yes')};"
        "CHARSET=UTF-16"
    )
    try:
        conn = pyodbc.connect(conn_str, timeout=10)
        print("数据库连接成功。")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

# 检查/创建表
def ensure_table(conn):
    cursor = conn.cursor()
    # 检查表是否存在
    check_sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Documents'"
    cursor.execute(check_sql)
    exists = cursor.fetchone()[0]
    if not exists:
        create_sql = '''
        CREATE TABLE Documents (
            id INT IDENTITY(1,1) PRIMARY KEY,
            doc_create_time DATETIME,
            file_name NVARCHAR(255),
            file_size BIGINT,
            doc_hash NVARCHAR(64),
            doc_content NVARCHAR(MAX),
            doc_creator NVARCHAR(255),
            upload_time DATETIME,
            uploader NVARCHAR(255),
            doc_keywords NVARCHAR(255),
            doc_title NVARCHAR(255),
            doc_abstract NVARCHAR(1024),
            doc_source NVARCHAR(255),
            doc_type NVARCHAR(64),
            doc_department NVARCHAR(255),
            doc_profession NVARCHAR(255),
            doc_analysis_time DATETIME DEFAULT '1999-01-01 00:00'
        )
        '''
        try:
            cursor.execute(create_sql)
            conn.commit()
            print("表单已创建。")
        except Exception as e:
            print(f"表单创建失败: {e}")
            sys.exit(1)
    else:
        # 检查file_size字段是否存在，不存在则添加
        try:
            cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Documents' AND COLUMN_NAME='file_size'")
            col_exists = cursor.fetchone()[0]
            if not col_exists:
                cursor.execute("ALTER TABLE Documents ADD file_size BIGINT")
                conn.commit()
                print("已为Documents表添加file_size字段。")
        except Exception as e:
            print(f"file_size字段检查/添加失败: {e}")
        print("表单已存在，无需创建。")

# 获取所有目标文件
def get_all_files(input_dir):
    exts = ('.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.wps', '.et')
    files = []
    for root, _, filenames in os.walk(input_dir):
        for fn in filenames:
            if fn.lower().endswith(exts):
                files.append(os.path.join(root, fn))
    return files

# 生成20位uuid
def uuid20():
    return uuid.uuid4().hex[:20]

# 计算文件hash
def file_hash(path):
    h = hashlib.sha256()
    with open(path, 'rb') as f:
        while True:
            chunk = f.read(8192)
            if not chunk:
                break
            h.update(chunk)
    return h.hexdigest()

# 数据库插入重试机制
def db_insert_with_retry(cursor, sql, params, max_retries=5, delay=2):
    for attempt in range(max_retries):
        try:
            cursor.execute(sql, params)
            return True
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"数据库插入失败，重试中({attempt+1}/{max_retries})... 错误: {e}")
                time.sleep(delay)
            else:
                print(f"数据库插入最终失败: {e}")
                return False

# 移动失败文档到指定目录
def move_failed_document(filepath, bad_doc_dir, reason):
    """将处理失败的文档移动到坏文档目录"""
    try:
        # 确保坏文档目录存在
        os.makedirs(bad_doc_dir, exist_ok=True)

        # 生成新的文件名，包含失败原因
        base_name = os.path.basename(filepath)
        name, ext = os.path.splitext(base_name)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # 清理原因字符串，移除不适合文件名的字符
        clean_reason = "".join(c for c in reason if c.isalnum() or c in "._-")[:50]
        new_name = f"{name}_{timestamp}_{clean_reason}{ext}"

        dest_path = os.path.join(bad_doc_dir, new_name)

        # 移动文件
        shutil.move(filepath, dest_path)
        return dest_path
    except Exception as e:
        print(f"移动失败文档时出错: {e}")
        return None

# 多线程处理单个文件
def process_file(f, output_dir, bad_doc_dir=None):
    try:
        if not resource_mgr.check_memory():
            return {"file": f, "status": "error", "msg": "系统内存不足，跳过处理"}

        ext = os.path.splitext(f)[1].lower()
        file_size = os.path.getsize(f)
        doc_hash = file_hash(f)

        # 从连接池获取连接
        conn = resource_mgr.get_db_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM Documents WHERE doc_hash = ?", doc_hash)
            exists = cursor.fetchone()[0]
            if exists:
                return {"file": f, "status": "skip_hash", "msg": "hash已存在"}

            # 对于XLSX文件，优先使用非LibreOffice方法
            doc_content = ""
            doc_create_time = None
            doc_creator = ""
            processing_failed = False
            failure_reason = ""

            if ext == '.xlsx':
                # XLSX文件优先使用openpyxl，避免LibreOffice进程竞争
                try:
                    from extract_utils import extract_xlsx_content_and_meta
                    print(f"使用openpyxl处理XLSX文件: {os.path.basename(f)}")
                    doc_content, doc_create_time, doc_creator = extract_xlsx_content_and_meta(f)
                except Exception as e:
                    print(f"openpyxl处理失败: {e}，将使用LibreOffice")
                    doc_content = ""
                    failure_reason = f"openpyxl失败: {str(e)[:100]}"

            # 如果XLSX的openpyxl方法失败，或者是其他格式，使用通用方法
            if not doc_content:
                # 只有在必要时才使用LibreOffice进程
                need_libreoffice = ext in ['.doc', '.xls', '.ppt', '.wps', '.et'] or not doc_content

                if need_libreoffice:
                    try:
                        with resource_mgr.office_process_limit():
                            from extract_utils import extract_file_content_and_meta, get_file_create_time, extract_doc_content_libreoffice
                            print(f"使用LibreOffice处理文件: {os.path.basename(f)}")
                            doc_content, doc_create_time, doc_creator = extract_file_content_and_meta(f)

                            # 如果没有提取到内容，尝试备用方法
                            if not doc_content:
                                doc_content = extract_doc_content_libreoffice(f)
                    except Exception as e:
                        processing_failed = True
                        failure_reason = f"LibreOffice处理失败: {str(e)[:100]}"
                        print(f"LibreOffice处理失败: {e}")
                else:
                    # 对于现代格式，直接使用Python库
                    try:
                        from extract_utils import extract_file_content_and_meta, get_file_create_time
                        print(f"使用Python库处理文件: {os.path.basename(f)}")
                        doc_content, doc_create_time, doc_creator = extract_file_content_and_meta(f)
                    except Exception as e:
                        processing_failed = True
                        failure_reason = f"Python库处理失败: {str(e)[:100]}"
                        print(f"Python库处理失败: {e}")

            # 检查处理结果
            if processing_failed or not doc_content:
                if file_size < 1024:  # 小于1KB的文件认为是空文件
                    return {"file": f, "status": "skip_content", "msg": "文件过小或为空文档"}
                else:
                    # 移动失败的文档到坏文档目录
                    if bad_doc_dir:
                        if not failure_reason:
                            failure_reason = "无法提取内容_可能损坏或不支持格式"
                        moved_path = move_failed_document(f, bad_doc_dir, failure_reason)
                        if moved_path:
                            return {"file": f, "status": "moved_to_bad", "msg": f"已移动到坏文档目录: {os.path.basename(moved_path)}"}
                        else:
                            return {"file": f, "status": "fail", "msg": f"处理失败且移动失败: {failure_reason}"}
                    else:
                        return {"file": f, "status": "fail", "msg": f"处理失败: {failure_reason or '无法提取内容'}"}

            if not doc_create_time:
                from extract_utils import get_file_create_time
                doc_create_time = get_file_create_time(f)
            if not doc_creator:
                doc_creator = ""

            new_name = f"{uuid20()}{ext}"
            out_path = os.path.join(output_dir, new_name)
            shutil.copy2(f, out_path)
            file_size = os.path.getsize(out_path)
            file_name = new_name
            upload_time = datetime.datetime.now()
            uploader = "Zhang Pengfei"

            sql = (
                "INSERT INTO Documents (doc_create_time, file_name, file_size, doc_hash, doc_content, doc_creator, upload_time, uploader, doc_keywords, doc_title, doc_abstract, doc_source, doc_type, doc_department, doc_profession, doc_analysis_time) "
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            )
            params = (
                doc_create_time, file_name, file_size, doc_hash, doc_content, doc_creator, upload_time, uploader,
                "", "", "", "", "", "", "", datetime.datetime(1999, 1, 1, 0, 0)
            )
            ok = db_insert_with_retry(cursor, sql, params)
            if ok:
                conn.commit()
                return {"file": f, "status": "success", "msg": f"已入库:{file_name}"}
            else:
                # 数据库插入失败，也移动到坏文档目录
                if bad_doc_dir:
                    moved_path = move_failed_document(f, bad_doc_dir, "数据库插入失败")
                    if moved_path:
                        return {"file": f, "status": "moved_to_bad", "msg": f"数据库插入失败，已移动: {os.path.basename(moved_path)}"}
                return {"file": f, "status": "fail", "msg": "数据库插入失败"}
        finally:
            resource_mgr.return_db_connection(conn)
    except Exception as e:
        # 处理过程中发生异常，移动到坏文档目录
        if bad_doc_dir:
            moved_path = move_failed_document(f, bad_doc_dir, f"处理异常_{str(e)[:50]}")
            if moved_path:
                return {"file": f, "status": "moved_to_bad", "msg": f"处理异常，已移动: {os.path.basename(moved_path)}"}
        return {"file": f, "status": "error", "msg": str(e)}

# 主流程
def save_progress(progress_file, processed_files):
    """保存处理进度到JSON文件"""
    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump({
                'processed_files': list(processed_files),
                'timestamp': datetime.datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存进度失败: {e}")

def load_progress(progress_file):
    """从JSON文件加载处理进度"""
    try:
        if os.path.exists(progress_file):
            with open(progress_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return set(data['processed_files'])
        return set()
    except Exception as e:
        print(f"加载进度失败: {e}")
        return set()

def main():
    parser = argparse.ArgumentParser(description="文档保存系统")
    parser.add_argument('--config', default='nbfwq.json', help='数据库配置文件路径')
    parser.add_argument('--input_dir', required=True, help='输入目录')
    parser.add_argument('--output_dir', required=True, help='输出目录')
    parser.add_argument('--threads', type=int, default=4, help='并发线程数，默认4')
    parser.add_argument('--progress_file', default='process_progress.json', help='进度保存文件路径')
    parser.add_argument('--resume', action='store_true', help='是否从上次进度继续处理')
    parser.add_argument('--bad_doc_dir', default='/data/baddocument', help='失败文档存放目录')
    parser.add_argument('--disable_bad_doc_move', action='store_true', help='禁用失败文档移动功能')
    args = parser.parse_args()

    cfg = load_config(args.config)
    conn = get_db_conn(cfg)
    ensure_table(conn)

    os.makedirs(args.output_dir, exist_ok=True)

    # 设置坏文档目录
    bad_doc_dir = None if args.disable_bad_doc_move else args.bad_doc_dir
    if bad_doc_dir:
        os.makedirs(bad_doc_dir, exist_ok=True)
        print(f"失败文档将移动到: {bad_doc_dir}")
    else:
        print("失败文档移动功能已禁用")

    files = get_all_files(args.input_dir)
    print(f"共发现{len(files)}个待处理文档。")

    # 加载上次进度
    processed_files = set()
    if args.resume:
        processed_files = load_progress(args.progress_file)
        print(f"从进度文件加载已处理文件数: {len(processed_files)}")

    # 过滤出未处理的文件
    files_to_process = [f for f in files if f not in processed_files]
    print(f"待处理文件数: {len(files_to_process)}")

    # 初始化资源管理器
    resource_mgr.start_monitoring()

    # 构造连接串
    conn_params = (
        f"DRIVER={cfg['driver']};"
        f"SERVER={cfg['server']};"
        f"DATABASE={cfg['database']};"
        f"UID={cfg['uid']};"
        f"PWD={cfg['pwd']};"
        f"Encrypt={cfg.get('encrypt', 'yes')};"
        f"TrustServerCertificate={cfg.get('trustServerCertificate', 'yes')};"
        "CHARSET=UTF-16"
    )

    # 初始化数据库连接池（优化：增加连接池大小）
    resource_mgr.init_db_pool(conn_params, pool_size=min(args.threads * 3, 20))

    # 分批处理文件，减少内存压力（优化：减少批量大小）
    batch_size = 500
    results = []

    # 性能监控
    start_time = time.time()
    success_count = 0
    error_count = 0
    moved_count = 0

    try:
        for i in range(0, len(files_to_process), batch_size):
            batch_files = files_to_process[i:i+batch_size]
            print(f"\n处理第{i//batch_size + 1}批文件（{len(batch_files)}个）...")

            with ThreadPoolExecutor(max_workers=args.threads) as executor:
                future_to_file = {executor.submit(process_file, f, args.output_dir, bad_doc_dir): f for f in batch_files}
                for future in tqdm(as_completed(future_to_file), total=len(batch_files), desc="多线程处理", ncols=100):
                    res = future.result()
                    results.append(res)
                    tqdm.write(f"{os.path.basename(res['file'])}: {res['status']} - {res['msg']}")

                    # 统计处理结果
                    if res['status'] == 'success':
                        success_count += 1
                    elif res['status'] == 'moved_to_bad':
                        moved_count += 1
                    elif res['status'] in ['error', 'fail']:
                        error_count += 1

                    # 更新并保存进度
                    if res['status'] not in ['error', 'fail']:
                        processed_files.add(res['file'])
                        save_progress(args.progress_file, processed_files)

    except KeyboardInterrupt:
        print("\n检测到中断，保存当前进度...")
        save_progress(args.progress_file, processed_files)
        sys.exit(1)

    # 性能报告
    end_time = time.time()
    total_time = end_time - start_time
    total_processed = success_count + moved_count + error_count

    print("\n" + "="*60)
    print("处理完成 - 性能报告")
    print("="*60)
    print(f"总处理时间: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
    print(f"总处理文件: {total_processed}个")
    print(f"成功入库: {success_count}个 ({success_count/total_processed*100:.1f}%)")
    print(f"移动到坏文档目录: {moved_count}个 ({moved_count/total_processed*100:.1f}%)")
    print(f"处理失败: {error_count}个 ({error_count/total_processed*100:.1f}%)")
    if total_time > 0:
        throughput = total_processed / total_time * 60
        print(f"处理速度: {throughput:.1f}文件/分钟")

    # 系统资源使用情况
    memory = psutil.virtual_memory()
    print(f"内存使用: {memory.percent:.1f}%")
    print("="*60)

    # 清理/data/save_doc/目录下未入库的文件
    print("开始清理/data/save_doc/目录下未入库的文件...")
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT file_name FROM Documents")
        db_files = set(row[0] for row in cursor.fetchall())
        save_dir = args.output_dir
        for fn in os.listdir(save_dir):
            fpath = os.path.join(save_dir, fn)
            if os.path.isfile(fpath) and fn not in db_files:
                try:
                    os.remove(fpath)
                    print(f"已清理未入库文件: {fn}")
                except Exception as e:
                    print(f"删除文件{fn}失败: {e}")
        print("清理完成。")
    except Exception as e:
        print(f"清理未入库文件时出错: {e}")

    # 处理完成后删除进度文件
    try:
        if os.path.exists(args.progress_file):
            os.remove(args.progress_file)
    except Exception as e:
        print(f"删除进度文件失败: {e}")

if __name__ == '__main__':
    main()
