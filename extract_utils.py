import os
import subprocess
import datetime

from docx import Document as DocxDocument
from pptx import Presentation as PptxPresentation
import xlrd
import openpyxl

def get_file_size_mb(filepath):
    """获取文件大小（MB）"""
    return os.path.getsize(filepath) / (1024 * 1024)

def get_convert_timeout(filepath, default_timeout=120):
    """根据文件大小确定转换超时时间"""
    size_mb = get_file_size_mb(filepath)
    if size_mb > 1:  # 大于1MB的文件
        # 每MB增加30秒，最少120秒，最多600秒
        timeout = min(max(default_timeout, int(size_mb * 30)), 600)
        return timeout
    return default_timeout

def extract_docx_content_and_meta(filepath):
    try:
        doc = DocxDocument(filepath)
        content_parts = []

        for para in doc.paragraphs:
            para_content = []

            # 检查段落中的运行
            for run in para.runs:
                # 检查是否有图像
                if run._element.xpath('.//a:blip'):
                    para_content.append("[图像]")
                elif run.text.strip():
                    para_content.append(run.text)

            # 如果段落有内容，添加到结果中
            if para_content:
                content_parts.append("".join(para_content))
            elif para.text.strip():
                # 如果段落有文本但没有运行内容，直接添加段落文本
                content_parts.append(para.text)

        # 检查文档中的其他图像（不在段落中的）
        additional_images = 0
        for rel in doc.part.rels.values():
            if "image" in rel.target_ref:
                additional_images += 1

        # 如果有额外的图像，添加说明
        if additional_images > 0:
            # 计算已经在段落中标记的图像数量
            marked_images = sum(part.count("[图像]") for part in content_parts)
            remaining_images = additional_images - marked_images
            if remaining_images > 0:
                content_parts.append(f"[文档还包含 {remaining_images} 个其他图像]")

        # 处理表格
        for table in doc.tables:
            table_content = extract_docx_table_content(table)
            if table_content:
                content_parts.append(f"[表格]\n{table_content}")
            else:
                content_parts.append("[表格]")

        final_text = "\n".join(content_parts)

        props = doc.core_properties
        create_time = props.created if props.created else None
        creator = props.author if props.author else ""
        return final_text, create_time, creator
    except Exception as e:
        # 如果是宏启用文档或其他特殊格式，尝试用libreoffice处理
        if "macroEnabled" in str(e) or "not a Word file" in str(e):
            print(f"检测到特殊格式DOCX文件，尝试用LibreOffice处理: {e}")
            try:
                import tempfile
                with tempfile.TemporaryDirectory() as tmpdir:
                    result = subprocess.run([
                        'libreoffice', '--headless', '--convert-to', 'txt:Text',
                        '--outdir', tmpdir, filepath
                    ], capture_output=True, text=True, timeout=120)

                    txt_file = os.path.join(tmpdir, os.path.basename(filepath) + '.txt')
                    if os.path.exists(txt_file):
                        with open(txt_file, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        if content.strip():
                            return content, None, ""
                        else:
                            # 如果转换后还是空的，可能包含图像
                            file_size = os.path.getsize(filepath)
                            if file_size > 10240:  # 大于10KB
                                return f"该文档可能包含图像或其他非文本内容（文件大小：{file_size//1024}KB）", None, ""
            except Exception as e2:
                print(f"LibreOffice处理DOCX失败: {e2}")

        return "", None, ""

def extract_docx_table_content(table):
    """提取DOCX表格内容"""
    try:
        table_text = []
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip() if cell.text else ""
                row_text.append(cell_text)
            table_text.append("\t".join(row_text))
        return "\n".join(table_text)
    except:
        return ""

def extract_pptx_content_and_meta(filepath):
    try:
        prs = PptxPresentation(filepath)
        content_parts = []

        for slide_idx, slide in enumerate(prs.slides, 1):
            slide_content = []
            slide_content.append(f"=== 幻灯片 {slide_idx} ===")

            # 按照形状在幻灯片中的位置排序（从上到下，从左到右）
            shapes_with_pos = []
            for shape in slide.shapes:
                try:
                    # 获取形状位置
                    top = shape.top if hasattr(shape, 'top') else 0
                    left = shape.left if hasattr(shape, 'left') else 0
                    shapes_with_pos.append((top, left, shape))
                except:
                    shapes_with_pos.append((0, 0, shape))

            # 按位置排序
            shapes_with_pos.sort(key=lambda x: (x[0], x[1]))

            for _, _, shape in shapes_with_pos:
                if hasattr(shape, "text") and shape.text.strip():
                    # 添加文本内容
                    slide_content.append(shape.text.strip())
                elif hasattr(shape, "shape_type"):
                    # 添加图像占位符
                    if shape.shape_type == 13:  # 图片
                        slide_content.append("[图像]")
                    elif shape.shape_type == 3:  # 图表
                        slide_content.append("[图表]")
                    elif shape.shape_type == 19:  # 表格
                        # 尝试提取表格内容
                        try:
                            if hasattr(shape, 'table'):
                                table_content = extract_table_content(shape.table)
                                if table_content:
                                    slide_content.append(f"[表格]\n{table_content}")
                                else:
                                    slide_content.append("[表格]")
                            else:
                                slide_content.append("[表格]")
                        except:
                            slide_content.append("[表格]")
                    elif shape.shape_type == 6:  # 文本框
                        if hasattr(shape, "text") and shape.text.strip():
                            slide_content.append(shape.text.strip())
                    else:
                        # 其他类型的形状
                        slide_content.append(f"[形状:{shape.shape_type}]")

            if len(slide_content) > 1:  # 除了标题外还有内容
                content_parts.append("\n".join(slide_content))

        # pptx元数据
        props = prs.core_properties
        create_time = props.created if hasattr(props, "created") else None
        creator = props.author if hasattr(props, "author") else ""

        final_content = "\n\n".join(content_parts) if content_parts else ""
        return final_content, create_time, creator
    except Exception as e:
        print(f"PPTX处理异常: {e}")
        return "", None, ""

def extract_table_content(table):
    """提取表格内容"""
    try:
        table_text = []
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip() if cell.text else ""
                row_text.append(cell_text)
            table_text.append("\t".join(row_text))
        return "\n".join(table_text)
    except:
        return ""

def extract_xlsx_content_and_meta(filepath):
    try:
        # 检查文件大小并调整超时时间
        timeout = get_convert_timeout(filepath)
        print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")

        wb = openpyxl.load_workbook(filepath, read_only=True)
        content_parts = []

        for ws_idx, ws in enumerate(wb.worksheets, 1):
            sheet_content = []
            sheet_content.append(f"=== 工作表 {ws_idx}: {ws.title} ===")

            # 提取单元格数据
            has_data = False
            for row in ws.iter_rows(values_only=True):
                row_data = [str(cell) if cell is not None else "" for cell in row]
                if any(cell.strip() for cell in row_data):  # 如果行中有非空数据
                    sheet_content.append("\t".join(row_data))
                    has_data = True

            # 检查是否有图表或图像（通过工作表的绘图对象）
            try:
                # 检查图表
                if hasattr(ws, '_charts') and ws._charts:
                    for chart in ws._charts:
                        sheet_content.append("[图表]")

                # 检查图像
                if hasattr(ws, '_images') and ws._images:
                    for image in ws._images:
                        sheet_content.append("[图像]")

                # 检查绘图对象
                if hasattr(ws, '_drawing') and ws._drawing:
                    drawing_objects = 0
                    try:
                        for obj in ws._drawing:
                            drawing_objects += 1
                    except:
                        pass
                    if drawing_objects > 0:
                        sheet_content.append(f"[绘图对象: {drawing_objects}个]")
            except Exception as e:
                print(f"检查Excel图像时出错: {e}")

            if has_data or len(sheet_content) > 1:
                content_parts.append("\n".join(sheet_content))

        props = wb.properties
        create_time = props.created if hasattr(props, "created") else None
        creator = props.creator if hasattr(props, "creator") else ""

        final_content = "\n\n".join(content_parts) if content_parts else ""
        return final_content, create_time, creator
    except Exception as e:
        print(f"处理Excel文件失败: {e}")
        return "", None, ""

# 移除循环导入，使用简单的资源管理
import threading
_office_lock = threading.Semaphore(2)  # 最多2个LibreOffice进程

class SimpleResourceManager:
    def office_process_limit(self):
        return _office_lock

resource_mgr = SimpleResourceManager()

def extract_doc_content_libreoffice(filepath):
    # 用libreoffice转txt
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")

            out_txt = filepath + ".txt"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "txt:Text",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            try:
                with open(out_txt, "r", encoding="utf-8", errors="ignore") as f:
                    content = f.read()
                return content
            finally:
                try:
                    if os.path.exists(out_txt):
                        os.remove(out_txt)
                except:
                    pass
    except subprocess.TimeoutExpired:
        print(f"文档转换超时: {filepath}")
        return ""
    except Exception as e:
        print(f"文档转换失败: {e}")
        return ""

def extract_doc_content_antiword(filepath):
    # 用antiword命令行工具提取doc文本
    try:
        result = subprocess.run(['antiword', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_xls_content_xls2csv(filepath):
    # 用xls2csv命令行工具提取xls文本
    try:
        result = subprocess.run(['xls2csv', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_ppt_content_catppt(filepath):
    # 用catppt命令行工具提取ppt文本
    try:
        result = subprocess.run(['catppt', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_ppt_content_with_image_detection(filepath):
    """PPT文件内容提取，包含图像检测"""
    # 首先尝试提取文本
    text = extract_ppt_content_catppt(filepath)

    # 尝试转换为PPTX格式进行更详细的处理
    try:
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            out_pptx = os.path.join(tmpdir, os.path.basename(filepath) + '.pptx')
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "pptx",
                "--outdir",
                tmpdir,
                filepath
            ]
            result = subprocess.run(cmd, capture_output=True, timeout=120)

            if os.path.exists(out_pptx):
                # 使用PPTX方法提取详细内容
                pptx_text, _, _ = extract_pptx_content_and_meta(out_pptx)
                if pptx_text and pptx_text.strip():
                    return pptx_text
    except Exception as e:
        print(f"PPT转PPTX处理失败: {e}")

    # 如果转换失败，使用原始文本
    if text and text.strip():
        return text

    # 最后尝试libreoffice转txt
    text = extract_doc_content_libreoffice(filepath)
    if text and text.strip():
        return text

    # 如果都失败了，根据文件大小判断是否可能包含图像
    file_size = os.path.getsize(filepath)
    if file_size > 10240:  # 大于10KB
        return f"该演示文稿可能包含图像或其他非文本内容（文件大小：{file_size//1024}KB）"

    return ""

def extract_xls_content_pyexcel(filepath):
    try:
        import pyexcel
        records = pyexcel.iget_records(file_name=filepath)
        text_rows = []
        for record in records:
            text_rows.append("\t".join([str(v) for v in record.values()]))
        return "\n".join(text_rows)
    except Exception:
        return ""

def extract_xls_content_libreoffice_xlsx(filepath):
    # 用libreoffice转xlsx再用openpyxl读取
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")

            out_xlsx = filepath + ".xlsx"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "xlsx",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_xlsx):
                try:
                    text = extract_xlsx_content_and_meta(out_xlsx)[0]
                    return text
                finally:
                    try:
                        os.remove(out_xlsx)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"文档转换超时: {filepath}")
        return ""
    except Exception as e:
        print(f"文档转换失败: {e}")
        return ""

def extract_xls_content_multimethod(filepath):
    # 1. xlrd
    try:
        wb = xlrd.open_workbook(filepath)
        text_rows = []
        for sheet in wb.sheets():
            for row_idx in range(sheet.nrows):
                row = sheet.row_values(row_idx)
                text_rows.append("\t".join([str(cell) for cell in row]))
        text = "\n".join(text_rows)
        if text.strip():
            return text
    except Exception:
        pass
    # 2. pyexcel
    text = extract_xls_content_pyexcel(filepath)
    if text.strip():
        return text
    # 3. xls2csv
    text = extract_xls_content_xls2csv(filepath)
    if text.strip():
        return text
    # 4. libreoffice转xlsx再openpyxl
    text = extract_xls_content_libreoffice_xlsx(filepath)
    return text

def extract_wps_content_antiword(filepath):
    # 尝试用antiword直接读取wps文件
    try:
        result = subprocess.run(['antiword', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_wps_content_txt(filepath):
    # 尝试直接转换为txt
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")

            out_txt = filepath + ".txt"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "txt:Text",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_txt):
                try:
                    with open(out_txt, "r", encoding="utf-8", errors="ignore") as f:
                        return f.read()
                finally:
                    try:
                        os.remove(out_txt)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"WPS转TXT超时: {filepath}")
        return ""
    except Exception as e:
        print(f"WPS转TXT失败: {e}")
        return ""

def extract_wps_content_libreoffice(filepath):
    # 用libreoffice将wps转为docx再读取
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")

            out_docx = filepath + ".docx"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "docx",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_docx):
                try:
                    text = extract_docx_content_and_meta(out_docx)[0]
                    return text
                finally:
                    try:
                        os.remove(out_docx)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"WPS转DOCX超时: {filepath}")
        return ""
    except Exception as e:
        print(f"WPS转DOCX失败: {e}")
        return ""

def extract_wps_content_multimethod(filepath):
    # 1. antiword
    text = extract_wps_content_antiword(filepath)
    if text.strip():
        return text
    # 2. 转txt
    text = extract_wps_content_txt(filepath)
    if text.strip():
        return text
    # 3. libreoffice转docx
    text = extract_wps_content_libreoffice(filepath)
    return text

def extract_et_content_xls2csv(filepath):
    # 尝试用xls2csv直接读取et文件
    try:
        result = subprocess.run(['xls2csv', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_et_content_pyexcel(filepath):
    # 尝试用pyexcel读取et文件
    try:
        import pyexcel
        records = pyexcel.iget_records(file_name=filepath)
        text_rows = []
        for record in records:
            text_rows.append("\t".join([str(v) for v in record.values()]))
        return "\n".join(text_rows)
    except Exception:
        return ""

def extract_et_content_libreoffice(filepath):
    # 用libreoffice将et转为xlsx再读取
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")

            out_xlsx = filepath + ".xlsx"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "xlsx",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_xlsx):
                try:
                    text = extract_xlsx_content_and_meta(out_xlsx)[0]
                    return text
                finally:
                    try:
                        os.remove(out_xlsx)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"ET转换超时: {filepath}")
        return ""
    except Exception as e:
        print(f"ET转换失败: {e}")
        return ""

def extract_et_content_multimethod(filepath):
    # 1. pyexcel
    text = extract_et_content_pyexcel(filepath)
    if text.strip():
        return text
    # 2. xls2csv
    text = extract_et_content_xls2csv(filepath)
    if text.strip():
        return text
    # 3. libreoffice转xlsx
    text = extract_et_content_libreoffice(filepath)
    return text

def analyze_file_content_type(filepath):
    """分析文件内容类型，为无法提取文本的文件生成描述"""
    try:
        import subprocess

        # 获取文件基本信息
        ext = os.path.splitext(filepath)[1].lower()
        file_size = os.path.getsize(filepath)

        # 使用file命令检测文件类型
        try:
            result = subprocess.run(['file', filepath], capture_output=True, text=True, timeout=10)
            file_info = result.stdout.strip().lower()
        except:
            file_info = ""

        # 根据文件信息和扩展名生成描述
        if "empty" in file_info:
            return f"空{ext.upper()}文档"
        elif "corrupt" in file_info or "damaged" in file_info:
            return f"损坏的{ext.upper()}文档（{file_size//1024}KB）"
        elif ext in ['.ppt', '.pptx']:
            if "microsoft powerpoint" in file_info or "presentation" in file_info:
                return f"PowerPoint演示文稿（{file_size//1024}KB），主要包含图像、图表或多媒体内容"
            else:
                return f"演示文稿文件（{file_size//1024}KB），可能包含图像、图表或动画"
        elif ext in ['.doc', '.docx']:
            if "microsoft word" in file_info or "document" in file_info:
                return f"Word文档（{file_size//1024}KB），主要包含图像、表格或特殊格式内容"
            else:
                return f"文档文件（{file_size//1024}KB），可能包含图像、表格或特殊格式"
        elif ext in ['.xls', '.xlsx']:
            if "microsoft excel" in file_info or "spreadsheet" in file_info:
                return f"Excel电子表格（{file_size//1024}KB），主要包含图表、图像或复杂数据"
            else:
                return f"电子表格文件（{file_size//1024}KB），可能包含图表或复杂格式"
        elif ext == '.wps':
            return f"WPS文档（{file_size//1024}KB），可能包含图像或特殊格式内容"
        elif ext == '.et':
            return f"ET电子表格（{file_size//1024}KB），可能包含图表或复杂数据"
        else:
            return f"Office文档（{file_size//1024}KB），包含非文本内容"

    except Exception as e:
        # 如果分析失败，返回基本描述
        ext = os.path.splitext(filepath)[1].lower()
        file_size = os.path.getsize(filepath)
        return f"{ext.upper()}文件（{file_size//1024}KB），无法提取文本内容"

def truncate_text(text, max_length=10000):
    """截取文本，保留指定长度"""
    if not text:
        return text

    if len(text) <= max_length:
        return text

    # 尝试在句子或段落边界截断
    for pos in range(max_length - 1, max_length - 100, -1):
        if pos >= len(text):
            continue
        # 优先在段落处截断
        if text[pos] == '\n':
            return text[:pos].strip()
        # 其次在句号处截断
        if text[pos] in ['。', '.', '!', '！', '?', '？']:
            return text[:pos+1].strip()

    # 如果找不到合适的断点，直接截取
    return text[:max_length].strip()

def extract_file_content_and_meta(filepath):
    ext = os.path.splitext(filepath)[1].lower()
    text, create_time, creator = "", None, ""
    # docx
    if ext == ".docx":
        text, create_time, creator = extract_docx_content_and_meta(filepath)
    # pptx
    elif ext == ".pptx":
        text, create_time, creator = extract_pptx_content_and_meta(filepath)
    # xlsx
    elif ext == ".xlsx":
        text, create_time, creator = extract_xlsx_content_and_meta(filepath)
    # doc
    elif ext == ".doc":
        # 1. antiword
        text = extract_doc_content_antiword(filepath)
        if not text:
            # 2. libreoffice
            text = extract_doc_content_libreoffice(filepath)
        create_time, creator = None, ""
    # xls
    elif ext == ".xls":
        text = extract_xls_content_multimethod(filepath)
        create_time, creator = None, ""
    # ppt
    elif ext == ".ppt":
        text = extract_ppt_content_with_image_detection(filepath)
        create_time, creator = None, ""
    # wps
    elif ext == ".wps":
        text = extract_wps_content_multimethod(filepath)
        create_time, creator = None, ""
    # et
    elif ext == ".et":
        text = extract_et_content_multimethod(filepath)
        create_time, creator = None, ""
    else:
        text, create_time, creator = "", None, ""

    # 如果没有提取到文本，使用文件分析
    if not text or not text.strip():
        text = analyze_file_content_type(filepath)
    else:
        # 截取文本
        text = truncate_text(text)

    return text, create_time, creator

def get_file_create_time(filepath):
    # 文件系统时间
    stat = os.stat(filepath)
    return datetime.datetime.fromtimestamp(stat.st_mtime)
