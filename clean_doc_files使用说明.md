# clean_doc_files.py 使用说明

## 功能概述

`clean_doc_files.py` 是一个用于清理文档文件和数据库记录不一致问题的工具。它可以：

1. **清理孤立文件**：删除 `/data/save_doc/` 目录下存在但数据库中没有记录的文件
2. **清理孤立记录**：删除数据库中存在但对应文件不存在的记录

## 使用方法

### 基本语法
```bash
python clean_doc_files.py [选项]
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--config` | 数据库配置文件路径 | `nbfwq.json` |
| `--dir` | 文档目录路径 | `/data/save_doc/` |
| `--dry-run` | 模拟运行，不实际删除 | 否 |
| `--files-only` | 只清理孤立文件 | 否 |
| `--db-only` | 只清理数据库孤立记录 | 否 |
| `--skip-confirm` | 跳过确认提示 | 否 |

### 使用示例

#### 1. 模拟运行（推荐首次使用）
```bash
# 查看会删除哪些文件和记录，但不实际执行
python clean_doc_files.py --dry-run
```

#### 2. 完整清理（默认模式）
```bash
# 清理孤立文件和孤立记录
python clean_doc_files.py
```

#### 3. 只清理孤立文件
```bash
# 只删除目录中不在数据库中的文件
python clean_doc_files.py --files-only
```

#### 4. 只清理数据库孤立记录
```bash
# 只删除数据库中对应文件不存在的记录
python clean_doc_files.py --db-only
```

#### 5. 自动执行（跳过确认）
```bash
# 跳过确认提示，直接执行清理
python clean_doc_files.py --skip-confirm
```

#### 6. 指定自定义路径
```bash
# 使用自定义配置文件和目录
python clean_doc_files.py --config /path/to/config.json --dir /path/to/docs/
```

## 安全特性

### 1. 模拟运行模式
- 使用 `--dry-run` 参数可以预览将要删除的内容
- 不会实际删除任何文件或数据库记录
- 建议在首次使用时先进行模拟运行

### 2. 确认提示
- 默认情况下会要求用户确认操作
- 显示将要执行的操作类型
- 用户必须输入 'y' 才会继续执行

### 3. 事务安全
- 数据库操作使用事务机制
- 如果删除记录失败会自动回滚
- 确保数据库一致性

### 4. 详细日志
- 显示处理进度和详细信息
- 记录成功和失败的操作
- 提供统计信息

## 输出示例

```
开始清理 /data/save_doc/ 目录和数据库的不一致问题...
数据库连接成功。
数据库中共有 1250 个文件记录
目录中共有 1248 个文件
需要删除 0 个文件（在目录中但不在数据库中）
文件清理完成: 删除 0 个文件, 失败 0 个

开始清理数据库中的孤立记录...
数据库中共有 1250 条记录
目录中共有 1248 个文件
需要删除 2 条数据库记录（文件不存在）
删除孤立记录: 100%|████████████| 2/2 [00:00<00:00, 45.23it/s]
已删除记录: ID=1001, 文件=missing_file1.docx
已删除记录: ID=1002, 文件=missing_file2.pdf
数据库清理完成: 删除 2 条记录, 失败 0 条

清理操作完成！
```

## 注意事项

### 1. 备份建议
- 在执行实际清理前，建议备份数据库
- 重要文件建议单独备份

### 2. 权限要求
- 需要对文档目录的读写权限
- 需要数据库的删除权限

### 3. 性能考虑
- 大量文件时处理可能需要较长时间
- 建议在系统负载较低时执行

### 4. 数据库连接
- 确保数据库配置文件正确
- 确保数据库服务正常运行

## 错误处理

### 常见错误及解决方法

1. **数据库连接失败**
   - 检查配置文件路径和内容
   - 确认数据库服务状态
   - 验证网络连接

2. **目录不存在**
   - 检查目录路径是否正确
   - 确认目录访问权限

3. **权限不足**
   - 确认对文件目录的写权限
   - 确认数据库删除权限

4. **文件删除失败**
   - 检查文件是否被其他程序占用
   - 确认文件权限设置

## 最佳实践

1. **首次使用**：先用 `--dry-run` 查看效果
2. **定期清理**：建议定期运行以保持一致性
3. **分步执行**：可以先用 `--files-only`，再用 `--db-only`
4. **监控日志**：注意查看输出信息，确认操作结果
5. **备份策略**：重要操作前先备份数据
