# 源文件自动删除功能说明

## 功能概述

为 `save_documents.py` 添加了源文件自动删除功能，处理完成后会自动删除input_dir中的源文件，只保留output_dir中的成功文件和bad_doc_dir中的失败文件，实现文件的自动整理和空间释放。

## 核心功能

### 1. 智能文件删除策略
- **成功入库**: 删除源文件，保留output_dir中的副本
- **处理失败**: 删除源文件，保留bad_doc_dir中的副本
- **删除失败**: 记录并报告，不影响主流程

### 2. 安全保护机制
- **确认提示**: 默认需要用户输入'DELETE'确认
- **参数控制**: 可通过参数禁用或强制启用
- **错误处理**: 删除失败时详细记录和报告

### 3. 详细状态跟踪
- **success**: 成功入库，源文件已删除
- **success_source_remain**: 成功入库，但源文件删除失败
- **moved_to_bad**: 已移动到坏文档目录，源文件已删除
- **moved_to_bad_source_remain**: 已移动到坏文档目录，但源文件删除失败

## 使用方法

### 1. 默认模式（需要确认）
```bash
python save_documents.py --input_dir /path/to/input --output_dir /path/to/output
```
程序会提示确认删除源文件，需要输入'DELETE'确认

### 2. 保留源文件模式
```bash
python save_documents.py \
  --input_dir /path/to/input \
  --output_dir /path/to/output \
  --keep_source
```
不删除任何源文件，保持原有行为

### 3. 强制删除模式（危险）
```bash
python save_documents.py \
  --input_dir /path/to/input \
  --output_dir /path/to/output \
  --force_delete
```
跳过确认提示，直接删除源文件

### 4. 完整配置示例
```bash
python save_documents.py \
  --input_dir /data/input \
  --output_dir /data/save_doc \
  --bad_doc_dir /data/baddocument \
  --threads 6 \
  --force_delete
```

## 安全机制

### 1. 确认提示
默认模式下会显示警告信息：
```
⚠️  警告: 程序将删除input_dir中的源文件！
   - 成功入库的文件：源文件将被删除，保留output_dir中的副本
   - 处理失败的文件：源文件将被删除，保留bad_doc_dir中的副本
   - 如果不想删除源文件，请使用 --keep_source 参数

确认要继续并删除源文件吗？(输入 'DELETE' 确认):
```

### 2. 参数验证
- `--keep_source` 和 `--force_delete` 不能同时使用
- 提供清晰的错误提示和使用建议

### 3. 错误处理
- 删除失败时不会中断程序
- 详细记录删除失败的文件
- 在最终报告中统计删除失败数量

## 处理流程

### 成功入库流程
1. 提取文档内容
2. 复制文件到output_dir
3. 插入数据库记录
4. **删除源文件**（新增）
5. 返回成功状态

### 失败处理流程
1. 尝试提取文档内容（失败）
2. 移动文件到bad_doc_dir
3. **删除源文件**（新增）
4. 返回失败状态

### 异常处理流程
1. 捕获处理异常
2. 移动文件到bad_doc_dir
3. **删除源文件**（新增）
4. 返回异常状态

## 状态说明

### 新增状态
- **success**: 成功入库，源文件已删除
- **success_source_remain**: 成功入库，但源文件删除失败
- **moved_to_bad**: 已移动到坏文档目录，源文件已删除
- **moved_to_bad_source_remain**: 已移动到坏文档目录，但源文件删除失败

### 现有状态
- **skip_hash**: 文档hash已存在，跳过处理（不删除源文件）
- **skip_content**: 文件过小或为空文档（不删除源文件）
- **error**: 处理过程中发生错误
- **fail**: 处理失败

## 性能报告增强

### 新增统计项
```
============================================================
处理完成 - 性能报告
============================================================
总处理时间: 1234.56秒 (20.6分钟)
总处理文件: 1000个
成功入库: 850个 (85.0%)
移动到坏文档目录: 120个 (12.0%)
处理失败: 30个 (3.0%)
源文件删除失败: 5个                    ← 新增
⚠️  注意: 部分源文件删除失败，请手动检查和清理  ← 新增
处理速度: 48.6文件/分钟
内存使用: 65.2%
============================================================
```

## 故障排除

### 常见问题

#### 1. 源文件删除失败
**原因**: 
- 文件被其他程序占用
- 权限不足
- 磁盘错误

**解决方案**:
```bash
# 检查文件占用
lsof /path/to/file

# 检查权限
ls -la /path/to/file

# 手动删除
rm /path/to/file
```

#### 2. 误删除源文件
**预防**: 
- 首次使用时用 `--keep_source` 测试
- 重要文件先备份
- 使用 `--dry_run` 模式（如果有）

**恢复**: 
- 从output_dir或bad_doc_dir恢复文件
- 从备份恢复

#### 3. 磁盘空间不足
**监控**: 
```bash
# 监控磁盘使用
df -h

# 监控目录大小
du -sh /data/input /data/save_doc /data/baddocument
```

## 维护建议

### 1. 定期检查
```bash
# 检查是否有残留的源文件
find /data/input -type f -name "*.doc*" -o -name "*.xls*" -o -name "*.ppt*"

# 检查删除失败的文件
grep "删除源文件失败" /path/to/logfile
```

### 2. 空间管理
```bash
# 监控各目录大小
du -sh /data/input /data/save_doc /data/baddocument

# 清理旧的坏文档
find /data/baddocument -mtime +30 -delete
```

### 3. 备份策略
- 重要文件处理前先备份
- 定期备份output_dir和数据库
- 保留关键文档的多个副本

## 最佳实践

### 1. 首次使用
```bash
# 1. 先用保留源文件模式测试
python save_documents.py --input_dir /test --output_dir /test_out --keep_source

# 2. 确认无误后使用删除模式
python save_documents.py --input_dir /data --output_dir /data_out
```

### 2. 生产环境
```bash
# 使用强制删除模式，避免交互
python save_documents.py \
  --input_dir /data/input \
  --output_dir /data/save_doc \
  --bad_doc_dir /data/baddocument \
  --force_delete \
  --threads 6
```

### 3. 安全模式
```bash
# 保留源文件，手动清理
python save_documents.py \
  --input_dir /data/input \
  --output_dir /data/save_doc \
  --keep_source
```

## 总结

源文件自动删除功能提供了：

1. **自动化管理**: 处理完成后自动清理源文件
2. **安全保护**: 多重确认和错误处理机制
3. **灵活配置**: 支持保留、删除、强制删除等模式
4. **详细监控**: 完整的删除状态跟踪和报告
5. **错误恢复**: 删除失败时的详细记录和处理

这个功能将显著简化文档处理后的文件管理工作，自动释放存储空间，同时确保重要文件的安全性。
