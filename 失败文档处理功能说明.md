# 失败文档处理功能说明

## 功能概述

已为 `save_documents.py` 添加了失败文档自动移动功能，当文档处理失败时，会自动将失败的文档移动到指定的坏文档目录，便于后续分析和处理。

## 新增功能

### 1. 失败文档自动移动
- **目标目录**: `/data/baddocument` (可自定义)
- **移动条件**: 文档处理失败、内容提取失败、数据库插入失败
- **文件命名**: 原文件名_时间戳_失败原因.扩展名

### 2. 详细的失败原因记录
- **openpyxl失败**: XLSX文件使用openpyxl库处理失败
- **LibreOffice处理失败**: 使用LibreOffice转换失败
- **Python库处理失败**: 使用Python库处理失败
- **数据库插入失败**: 数据库操作失败
- **处理异常**: 其他未预期的异常

### 3. 灵活的配置选项
- **启用/禁用**: 可通过参数控制是否启用失败文档移动
- **自定义目录**: 可指定自定义的坏文档存放目录
- **安全处理**: 移动失败时不会影响主程序运行

## 使用方法

### 基本用法（默认启用）
```bash
python save_documents.py --input_dir /path/to/input --output_dir /path/to/output
```
失败的文档会自动移动到 `/data/baddocument` 目录

### 自定义坏文档目录
```bash
python save_documents.py \
  --input_dir /path/to/input \
  --output_dir /path/to/output \
  --bad_doc_dir /custom/bad/docs
```

### 禁用失败文档移动
```bash
python save_documents.py \
  --input_dir /path/to/input \
  --output_dir /path/to/output \
  --disable_bad_doc_move
```

## 文件命名规则

移动到坏文档目录的文件会按以下格式重命名：
```
原文件名_YYYYMMDD_HHMMSS_失败原因.扩展名
```

### 示例
- `report.xlsx` → `report_20241201_143022_openpyxl失败.xlsx`
- `document.doc` → `document_20241201_143055_LibreOffice处理失败.doc`
- `presentation.ppt` → `presentation_20241201_143128_无法提取内容_可能损坏或不支持格式.ppt`

## 处理状态说明

### 新增状态
- **moved_to_bad**: 文档已移动到坏文档目录
- **fail**: 处理失败但未移动（禁用移动功能时）

### 现有状态
- **success**: 成功处理并入库
- **skip_hash**: 文档hash已存在，跳过处理
- **skip_content**: 文件过小或为空文档
- **error**: 处理过程中发生错误

## 性能优化

### 已实施的优化
1. **LibreOffice进程数**: 从1个增加到2个
2. **数据库连接池**: 增加到最多20个连接
3. **批量处理大小**: 从1000减少到500，减少内存压力
4. **性能监控**: 添加详细的处理统计和性能报告

### 性能报告示例
```
============================================================
处理完成 - 性能报告
============================================================
总处理时间: 1234.56秒 (20.6分钟)
总处理文件: 1000个
成功入库: 850个 (85.0%)
移动到坏文档目录: 120个 (12.0%)
处理失败: 30个 (3.0%)
处理速度: 48.6文件/分钟
内存使用: 65.2%
============================================================
```

## 坏文档分析

### 常见失败原因
1. **文件损坏**: 文件结构损坏，无法正常读取
2. **格式不支持**: 特殊或过时的文件格式
3. **密码保护**: 文件有密码保护
4. **权限问题**: 文件访问权限不足
5. **内存不足**: 处理大文件时内存不足

### 分析建议
```bash
# 查看坏文档目录中的文件
ls -la /data/baddocument/

# 按失败原因分类统计
ls /data/baddocument/ | grep -o '_[^.]*\.' | sort | uniq -c

# 查看最近的失败文档
ls -lt /data/baddocument/ | head -10
```

## 故障排除

### 常见问题

#### 1. 坏文档目录创建失败
**原因**: 权限不足或磁盘空间不足
**解决**: 检查目录权限和磁盘空间

#### 2. 文件移动失败
**原因**: 源文件被占用或目标目录权限不足
**解决**: 检查文件状态和目录权限

#### 3. 移动后原文件仍存在
**原因**: 移动操作失败但程序继续运行
**解决**: 检查日志中的错误信息

### 日志信息
程序会输出详细的处理日志：
```
使用openpyxl处理XLSX文件: example.xlsx
openpyxl处理失败: File is not a zip file，将使用LibreOffice
LibreOffice处理失败: 等待libreoffice进程超时
example.xlsx: moved_to_bad - 已移动到坏文档目录: example_20241201_143022_LibreOffice处理失败.xlsx
```

## 维护建议

### 定期清理
```bash
# 定期清理超过30天的坏文档
find /data/baddocument -name "*.doc*" -mtime +30 -delete
find /data/baddocument -name "*.xls*" -mtime +30 -delete
find /data/baddocument -name "*.ppt*" -mtime +30 -delete
```

### 监控磁盘空间
```bash
# 监控坏文档目录大小
du -sh /data/baddocument

# 统计文件数量
find /data/baddocument -type f | wc -l
```

### 分析失败模式
```bash
# 统计失败原因
ls /data/baddocument/ | sed 's/.*_[0-9]*_[0-9]*_//' | sed 's/\.[^.]*$//' | sort | uniq -c | sort -nr
```

## 总结

失败文档处理功能提供了：

1. **自动化处理**: 失败文档自动移动，无需手动干预
2. **详细记录**: 失败原因记录在文件名中，便于分析
3. **灵活配置**: 支持自定义目录和启用/禁用功能
4. **性能优化**: 同时实施了多项性能优化措施
5. **完整监控**: 提供详细的处理统计和性能报告

这些改进将显著提高文档处理系统的可靠性和可维护性，同时提供更好的性能和用户体验。
