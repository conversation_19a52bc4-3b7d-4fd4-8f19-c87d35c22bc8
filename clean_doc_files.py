#!/usr/bin/env python3
# 清理/data/save_doc/目录下不存在于数据库中的文件

import os
import sys
import json
import pyodbc
import argparse
from tqdm import tqdm

def load_config(config_path):
    """读取数据库配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def get_db_conn(cfg):
    """连接数据库"""
    conn_str = (
        f"DRIVER={cfg['driver']};"
        f"SERVER={cfg['server']};"
        f"DATABASE={cfg['database']};"
        f"UID={cfg['uid']};"
        f"PWD={cfg['pwd']};"
        f"Encrypt={cfg.get('encrypt', 'yes')};"
        f"TrustServerCertificate={cfg.get('trustServerCertificate', 'yes')};"
        "CHARSET=UTF-16"
    )
    try:
        conn = pyodbc.connect(conn_str, timeout=10)
        print("数据库连接成功。")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def clean_files(doc_dir, conn, dry_run=False):
    """清理不在数据库中的文件"""
    # 获取数据库中所有文件名
    cursor = conn.cursor()
    cursor.execute("SELECT file_name FROM Documents")
    db_files = set(row[0] for row in cursor.fetchall())
    print(f"数据库中共有 {len(db_files)} 个文件记录")
    
    # 获取目录中所有文件
    if not os.path.exists(doc_dir):
        print(f"目录 {doc_dir} 不存在")
        return
    
    disk_files = [f for f in os.listdir(doc_dir) if os.path.isfile(os.path.join(doc_dir, f))]
    print(f"目录中共有 {len(disk_files)} 个文件")
    
    # 找出需要删除的文件
    to_delete = [f for f in disk_files if f not in db_files]
    print(f"需要删除 {len(to_delete)} 个文件")
    
    # 删除文件
    deleted = 0
    failed = 0
    
    for filename in tqdm(to_delete, desc="删除文件"):
        filepath = os.path.join(doc_dir, filename)
        try:
            if not dry_run:
                os.remove(filepath)
            deleted += 1
            if dry_run:
                tqdm.write(f"[模拟] 已删除: {filename}")
            else:
                tqdm.write(f"已删除: {filename}")
        except Exception as e:
            failed += 1
            tqdm.write(f"删除失败 {filename}: {e}")
    
    print(f"{'[模拟] ' if dry_run else ''}清理完成: 删除 {deleted} 个文件, 失败 {failed} 个")

def main():
    parser = argparse.ArgumentParser(description="清理不在数据库中的文档文件")
    parser.add_argument('--config', default='nbfwq.json', help='数据库配置文件路径')
    parser.add_argument('--dir', default='/data/save_doc/', help='文档目录路径')
    parser.add_argument('--dry-run', action='store_true', help='模拟运行，不实际删除文件')
    args = parser.parse_args()
    
    print(f"开始清理 {args.dir} 目录下不在数据库中的文件...")
    
    # 加载配置并连接数据库
    cfg = load_config(args.config)
    conn = get_db_conn(cfg)
    
    try:
        # 执行清理
        clean_files(args.dir, conn, args.dry_run)
    finally:
        conn.close()

if __name__ == '__main__':
    main()