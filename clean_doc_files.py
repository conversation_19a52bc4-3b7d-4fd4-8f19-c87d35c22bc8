#!/usr/bin/env python3
# 清理文档文件和数据库记录的不一致问题
# 功能：
# 1. 清理/data/save_doc/目录下不存在于数据库中的孤立文件
# 2. 清理数据库中对应文件不存在的孤立记录

import os
import sys
import json
import pyodbc
import argparse
from tqdm import tqdm

def load_config(config_path):
    """读取数据库配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def get_db_conn(cfg):
    """连接数据库"""
    conn_str = (
        f"DRIVER={cfg['driver']};"
        f"SERVER={cfg['server']};"
        f"DATABASE={cfg['database']};"
        f"UID={cfg['uid']};"
        f"PWD={cfg['pwd']};"
        f"Encrypt={cfg.get('encrypt', 'yes')};"
        f"TrustServerCertificate={cfg.get('trustServerCertificate', 'yes')};"
        "CHARSET=UTF-16"
    )
    try:
        conn = pyodbc.connect(conn_str, timeout=10)
        print("数据库连接成功。")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def clean_files(doc_dir, conn, dry_run=False):
    """清理不在数据库中的文件"""
    # 获取数据库中所有文件名
    cursor = conn.cursor()
    cursor.execute("SELECT file_name FROM Documents")
    db_files = set(row[0] for row in cursor.fetchall())
    print(f"数据库中共有 {len(db_files)} 个文件记录")

    # 获取目录中所有文件
    if not os.path.exists(doc_dir):
        print(f"目录 {doc_dir} 不存在")
        return

    disk_files = [f for f in os.listdir(doc_dir) if os.path.isfile(os.path.join(doc_dir, f))]
    disk_files_set = set(disk_files)
    print(f"目录中共有 {len(disk_files)} 个文件")

    # 找出需要删除的文件（在目录中但不在数据库中）
    files_to_delete = [f for f in disk_files if f not in db_files]
    print(f"需要删除 {len(files_to_delete)} 个文件（在目录中但不在数据库中）")

    # 删除文件
    deleted_files = 0
    failed_files = 0

    for filename in tqdm(files_to_delete, desc="删除孤立文件"):
        filepath = os.path.join(doc_dir, filename)
        try:
            if not dry_run:
                os.remove(filepath)
            deleted_files += 1
            if dry_run:
                tqdm.write(f"[模拟] 已删除文件: {filename}")
            else:
                tqdm.write(f"已删除文件: {filename}")
        except Exception as e:
            failed_files += 1
            tqdm.write(f"删除文件失败 {filename}: {e}")

    print(f"{'[模拟] ' if dry_run else ''}文件清理完成: 删除 {deleted_files} 个文件, 失败 {failed_files} 个")

def clean_database_records(doc_dir, conn, dry_run=False):
    """清理数据库中对应文件不存在的记录"""
    print("\n开始清理数据库中的孤立记录...")

    # 获取数据库中所有文件名和ID
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name FROM Documents")
    db_records = cursor.fetchall()
    print(f"数据库中共有 {len(db_records)} 条记录")

    # 检查目录是否存在
    if not os.path.exists(doc_dir):
        print(f"目录 {doc_dir} 不存在，无法检查文件")
        return

    # 获取目录中所有文件
    disk_files = set(f for f in os.listdir(doc_dir) if os.path.isfile(os.path.join(doc_dir, f)))
    print(f"目录中共有 {len(disk_files)} 个文件")

    # 找出需要删除的数据库记录（在数据库中但文件不存在）
    records_to_delete = []
    for record_id, filename in db_records:
        if filename not in disk_files:
            records_to_delete.append((record_id, filename))

    print(f"需要删除 {len(records_to_delete)} 条数据库记录（文件不存在）")

    if len(records_to_delete) == 0:
        print("没有需要删除的数据库记录")
        return

    # 删除数据库记录
    deleted_records = 0
    failed_records = 0

    for record_id, filename in tqdm(records_to_delete, desc="删除孤立记录"):
        try:
            if not dry_run:
                cursor.execute("DELETE FROM Documents WHERE id = ?", record_id)
                conn.commit()
            deleted_records += 1
            if dry_run:
                tqdm.write(f"[模拟] 已删除记录: ID={record_id}, 文件={filename}")
            else:
                tqdm.write(f"已删除记录: ID={record_id}, 文件={filename}")
        except Exception as e:
            failed_records += 1
            tqdm.write(f"删除记录失败 ID={record_id}, 文件={filename}: {e}")
            # 如果不是模拟运行，回滚事务
            if not dry_run:
                try:
                    conn.rollback()
                except:
                    pass

    print(f"{'[模拟] ' if dry_run else ''}数据库清理完成: 删除 {deleted_records} 条记录, 失败 {failed_records} 条")

def main():
    parser = argparse.ArgumentParser(description="清理文档文件和数据库记录的不一致问题")
    parser.add_argument('--config', default='nbfwq.json', help='数据库配置文件路径')
    parser.add_argument('--dir', default='/data/save_doc/', help='文档目录路径')
    parser.add_argument('--dry-run', action='store_true', help='模拟运行，不实际删除文件或记录')
    parser.add_argument('--files-only', action='store_true', help='只清理孤立文件，不清理数据库记录')
    parser.add_argument('--db-only', action='store_true', help='只清理数据库孤立记录，不清理文件')
    parser.add_argument('--skip-confirm', action='store_true', help='跳过确认提示，直接执行')
    args = parser.parse_args()

    # 检查参数冲突
    if args.files_only and args.db_only:
        print("错误: --files-only 和 --db-only 不能同时使用")
        sys.exit(1)

    print(f"开始清理 {args.dir} 目录和数据库的不一致问题...")
    if args.dry_run:
        print("*** 模拟运行模式 - 不会实际删除任何文件或记录 ***")

    # 加载配置并连接数据库
    cfg = load_config(args.config)
    conn = get_db_conn(cfg)

    try:
        # 根据参数决定执行哪些清理操作
        if args.db_only:
            # 只清理数据库记录
            if not args.skip_confirm and not args.dry_run:
                confirm = input("\n确认要删除数据库中的孤立记录吗？(y/N): ")
                if confirm.lower() != 'y':
                    print("操作已取消")
                    return
            clean_database_records(args.dir, conn, args.dry_run)

        elif args.files_only:
            # 只清理文件
            if not args.skip_confirm and not args.dry_run:
                confirm = input("\n确认要删除目录中的孤立文件吗？(y/N): ")
                if confirm.lower() != 'y':
                    print("操作已取消")
                    return
            clean_files(args.dir, conn, args.dry_run)

        else:
            # 执行完整清理（默认）
            if not args.skip_confirm and not args.dry_run:
                print("\n将执行以下操作:")
                print("1. 删除目录中不在数据库中的孤立文件")
                print("2. 删除数据库中对应文件不存在的孤立记录")
                confirm = input("\n确认要执行完整清理吗？(y/N): ")
                if confirm.lower() != 'y':
                    print("操作已取消")
                    return

            # 先清理孤立文件
            clean_files(args.dir, conn, args.dry_run)

            # 再清理数据库孤立记录
            clean_database_records(args.dir, conn, args.dry_run)

        print("\n清理操作完成！")

    finally:
        conn.close()

if __name__ == '__main__':
    main()