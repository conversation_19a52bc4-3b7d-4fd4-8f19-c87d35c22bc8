#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LibreOffice进程监控脚本
用于实时监控LibreOffice进程状态，帮助诊断超时问题
"""

import psutil
import time
import datetime
import os
import sys

def get_libreoffice_processes():
    """获取所有LibreOffice进程信息"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'status', 'create_time', 'memory_info', 'cpu_percent']):
        try:
            if 'soffice' in proc.info['name']:
                # 计算运行时间
                create_time = datetime.datetime.fromtimestamp(proc.info['create_time'])
                running_time = datetime.datetime.now() - create_time
                
                processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'status': proc.info['status'],
                    'create_time': create_time,
                    'running_time': running_time,
                    'memory_mb': proc.info['memory_info'].rss / 1024 / 1024,
                    'cpu_percent': proc.info['cpu_percent']
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    return processes

def print_process_info(processes):
    """打印进程信息"""
    if not processes:
        print("没有发现LibreOffice进程")
        return
    
    print(f"发现 {len(processes)} 个LibreOffice进程:")
    print("-" * 80)
    print(f"{'PID':<8} {'状态':<12} {'运行时间':<12} {'内存(MB)':<10} {'CPU%':<8} {'创建时间'}")
    print("-" * 80)
    
    for proc in processes:
        running_time_str = str(proc['running_time']).split('.')[0]  # 去掉微秒
        create_time_str = proc['create_time'].strftime('%H:%M:%S')
        
        print(f"{proc['pid']:<8} {proc['status']:<12} {running_time_str:<12} "
              f"{proc['memory_mb']:<10.1f} {proc['cpu_percent']:<8.1f} {create_time_str}")

def get_system_info():
    """获取系统资源信息"""
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    return {
        'memory_percent': memory.percent,
        'memory_available_gb': memory.available / 1024 / 1024 / 1024,
        'cpu_percent': cpu_percent
    }

def monitor_continuous():
    """连续监控模式"""
    print("LibreOffice进程连续监控 (按Ctrl+C退出)")
    print("=" * 80)
    
    try:
        while True:
            # 清屏
            os.system('clear' if os.name == 'posix' else 'cls')
            
            # 显示当前时间
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"监控时间: {current_time}")
            print("=" * 80)
            
            # 显示系统信息
            sys_info = get_system_info()
            print(f"系统状态: CPU {sys_info['cpu_percent']:.1f}% | "
                  f"内存 {sys_info['memory_percent']:.1f}% | "
                  f"可用内存 {sys_info['memory_available_gb']:.1f}GB")
            print()
            
            # 显示LibreOffice进程
            processes = get_libreoffice_processes()
            print_process_info(processes)
            
            # 检查异常情况
            warnings = []
            for proc in processes:
                if proc['running_time'].total_seconds() > 600:  # 运行超过10分钟
                    warnings.append(f"进程 {proc['pid']} 运行时间过长: {proc['running_time']}")
                if proc['memory_mb'] > 500:  # 内存使用超过500MB
                    warnings.append(f"进程 {proc['pid']} 内存使用过高: {proc['memory_mb']:.1f}MB")
                if proc['status'] == 'zombie':
                    warnings.append(f"进程 {proc['pid']} 是僵尸进程")
            
            if warnings:
                print("\n⚠️  警告:")
                for warning in warnings:
                    print(f"  - {warning}")
            
            print(f"\n下次更新: 10秒后... (总进程数: {len(processes)})")
            time.sleep(10)
            
    except KeyboardInterrupt:
        print("\n监控已停止")

def monitor_once():
    """单次监控模式"""
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"LibreOffice进程监控 - {current_time}")
    print("=" * 80)
    
    # 显示系统信息
    sys_info = get_system_info()
    print(f"系统状态: CPU {sys_info['cpu_percent']:.1f}% | "
          f"内存 {sys_info['memory_percent']:.1f}% | "
          f"可用内存 {sys_info['memory_available_gb']:.1f}GB")
    print()
    
    # 显示LibreOffice进程
    processes = get_libreoffice_processes()
    print_process_info(processes)
    
    # 统计信息
    if processes:
        total_memory = sum(proc['memory_mb'] for proc in processes)
        avg_running_time = sum(proc['running_time'].total_seconds() for proc in processes) / len(processes)
        
        print(f"\n统计信息:")
        print(f"  总进程数: {len(processes)}")
        print(f"  总内存使用: {total_memory:.1f}MB")
        print(f"  平均运行时间: {avg_running_time:.1f}秒")
        
        # 检查长时间运行的进程
        long_running = [p for p in processes if p['running_time'].total_seconds() > 300]
        if long_running:
            print(f"  长时间运行进程: {len(long_running)}个 (>5分钟)")

def main():
    if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
        monitor_continuous()
    else:
        monitor_once()
        print("\n提示: 使用 --continuous 参数进行连续监控")

if __name__ == "__main__":
    main()
