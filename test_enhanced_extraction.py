#!/usr/bin/env python3
"""
测试增强的文本提取功能
包含图像占位符的文本提取
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('/home/<USER>/archive_pull')

def test_enhanced_extraction():
    """测试增强的提取功能"""
    test_files = [
        "/data/ready_upload/f8769543088_1.pptx",  # 大的PPTX文件，包含图像
        "/data/ready_upload/f2775720160_1.pptx",  # 有文本的PPTX文件
        "/data/ready_upload/f1820358824_1.pptx",  # 另一个PPTX文件
        "/data/ready_upload/f9236352168.xlsx",    # XLSX文件
        "/data/ready_upload/f10203409896.xls",    # XLS文件
    ]
    
    print("测试增强的文本提取功能（包含图像占位符）")
    print("=" * 70)
    
    for i, filepath in enumerate(test_files, 1):
        if not os.path.exists(filepath):
            print(f"[{i}] 文件不存在: {os.path.basename(filepath)}")
            continue
            
        print(f"\n[{i}] 测试文件: {os.path.basename(filepath)}")
        print(f"文件大小: {os.path.getsize(filepath) / (1024*1024):.2f} MB")
        print("-" * 50)
        
        try:
            from extract_utils import extract_file_content_and_meta
            text, create_time, creator = extract_file_content_and_meta(filepath)
            
            if text and text.strip():
                print(f"✅ 提取成功")
                print(f"内容长度: {len(text)} 字符")
                print(f"创建时间: {create_time}")
                print(f"创建者: {creator}")
                
                # 统计占位符
                image_count = text.count("[图像]")
                chart_count = text.count("[图表]")
                table_count = text.count("[表格]")
                
                if image_count > 0 or chart_count > 0 or table_count > 0:
                    print(f"🖼️  包含元素: 图像{image_count}个, 图表{chart_count}个, 表格{table_count}个")
                
                print("内容预览:")
                print("=" * 40)
                # 显示前500字符
                preview = text[:500]
                if len(text) > 500:
                    preview += "..."
                print(preview)
                print("=" * 40)
            else:
                print("❌ 提取失败：无内容")
                
        except Exception as e:
            print(f"❌ 提取异常: {e}")
            import traceback
            traceback.print_exc()

def test_specific_pptx_detailed():
    """详细测试特定PPTX文件"""
    print(f"\n{'='*70}")
    print("详细测试PPTX文件的增强提取")
    print("=" * 70)
    
    filepath = "/data/ready_upload/f8769543088_1.pptx"
    
    if not os.path.exists(filepath):
        print("测试文件不存在")
        return
    
    print(f"测试文件: {os.path.basename(filepath)}")
    print(f"文件大小: {os.path.getsize(filepath) / (1024*1024):.2f} MB")
    
    try:
        from extract_utils import extract_pptx_content_and_meta
        text, create_time, creator = extract_pptx_content_and_meta(filepath)
        
        print(f"\n提取结果:")
        print(f"内容长度: {len(text)} 字符")
        print(f"创建时间: {create_time}")
        print(f"创建者: {creator}")
        
        if text:
            # 分析内容结构
            slides = text.split("=== 幻灯片")
            print(f"检测到 {len(slides)-1} 张幻灯片")
            
            # 统计各种元素
            total_images = text.count("[图像]")
            total_charts = text.count("[图表]")
            total_tables = text.count("[表格]")
            
            print(f"总计: 图像{total_images}个, 图表{total_charts}个, 表格{total_tables}个")
            
            print("\n完整内容:")
            print("-" * 50)
            print(text)
            print("-" * 50)
        else:
            print("未提取到内容")
            
    except Exception as e:
        print(f"详细测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_extraction()
    test_specific_pptx_detailed()
