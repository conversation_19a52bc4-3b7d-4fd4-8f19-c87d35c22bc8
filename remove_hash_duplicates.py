#!/usr/bin/env python3
# 对指定目录下的文件与数据库中的doc_hash字段进行查重，删除指定目录中的重复文件

import os
import sys
import json
import hashlib
import pyodbc
import argparse
from tqdm import tqdm
import concurrent.futures

def load_config(config_path):
    """读取数据库配置"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def get_db_conn(cfg):
    """连接数据库"""
    conn_str = (
        f"DRIVER={cfg['driver']};"
        f"SERVER={cfg['server']};"
        f"DATABASE={cfg['database']};"
        f"UID={cfg['uid']};"
        f"PWD={cfg['pwd']};"
        f"Encrypt={cfg.get('encrypt', 'yes')};"
        f"TrustServerCertificate={cfg.get('trustServerCertificate', 'yes')};"
        "CHARSET=UTF-16"
    )
    try:
        conn = pyodbc.connect(conn_str, timeout=10)
        print("数据库连接成功。")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

def get_db_hashes(conn):
    """获取数据库中所有文档的哈希值"""
    cursor = conn.cursor()
    cursor.execute("SELECT doc_hash, file_name FROM Documents WHERE doc_hash IS NOT NULL")
    db_hashes = {}
    for row in cursor.fetchall():
        hash_value = row[0]
        file_name = row[1]
        if hash_value:  # 确保哈希值不为空
            db_hashes[hash_value] = file_name
    return db_hashes

def calculate_hash(filepath):
    """计算文件的SHA256哈希值"""
    h = hashlib.sha256()
    try:
        with open(filepath, 'rb') as f:
            while True:
                chunk = f.read(8192)
                if not chunk:
                    break
                h.update(chunk)
        return h.hexdigest()
    except Exception as e:
        print(f"计算哈希值失败 {filepath}: {e}")
        return None

def get_files_with_hash(directory):
    """获取目录下所有文件及其哈希值"""
    if not os.path.exists(directory):
        print(f"目录 {directory} 不存在")
        return {}
    
    files = [os.path.join(directory, f) for f in os.listdir(directory) 
             if os.path.isfile(os.path.join(directory, f))]
    
    print(f"开始计算 {directory} 目录中 {len(files)} 个文件的哈希值...")
    
    result = {}
    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        future_to_file = {executor.submit(calculate_hash, f): f for f in files}
        
        for future in tqdm(concurrent.futures.as_completed(future_to_file), total=len(files), desc="计算哈希值"):
            file_path = future_to_file[future]
            try:
                file_hash = future.result()
                if file_hash:
                    result[file_path] = file_hash
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")
    
    return result

def find_and_remove_duplicates(source_dir, conn, dry_run=False):
    """查找并删除重复文件"""
    # 获取数据库中所有文档的哈希值
    db_hashes = get_db_hashes(conn)
    print(f"数据库中共有 {len(db_hashes)} 个文档哈希记录")
    
    # 获取源目录中所有文件的哈希值
    source_files = get_files_with_hash(source_dir)
    print(f"源目录 {source_dir} 中共有 {len(source_files)} 个文件")
    
    # 查找重复文件
    duplicates = []
    for source_path, source_hash in source_files.items():
        if source_hash in db_hashes:
            db_file = db_hashes[source_hash]
            duplicates.append((source_path, db_file, source_hash))
    
    print(f"发现 {len(duplicates)} 个重复文件")
    
    # 删除重复文件
    deleted = 0
    failed = 0
    
    for source_path, db_file, file_hash in tqdm(duplicates, desc="删除重复文件"):
        try:
            if not dry_run:
                os.remove(source_path)
            deleted += 1
            if dry_run:
                tqdm.write(f"[模拟] 已删除: {source_path} (与数据库文件 {db_file} 重复, 哈希值: {file_hash[:8]}...)")
            else:
                tqdm.write(f"已删除: {source_path} (与数据库文件 {db_file} 重复, 哈希值: {file_hash[:8]}...)")
        except Exception as e:
            failed += 1
            tqdm.write(f"删除失败 {source_path}: {e}")
    
    print(f"{'[模拟] ' if dry_run else ''}清理完成: 删除 {deleted} 个文件, 失败 {failed} 个")

def main():
    parser = argparse.ArgumentParser(description="查找并删除与数据库中哈希值重复的文件")
    parser.add_argument('--config', default='nbfwq.json', help='数据库配置文件路径')
    parser.add_argument('--source', required=True, help='源目录路径，将从此目录删除重复文件')
    parser.add_argument('--dry-run', action='store_true', help='模拟运行，不实际删除文件')
    args = parser.parse_args()
    
    print(f"开始查找 {args.source} 目录中与数据库中哈希值重复的文件...")
    
    # 加载配置并连接数据库
    cfg = load_config(args.config)
    conn = get_db_conn(cfg)
    
    try:
        # 执行查重和删除
        find_and_remove_duplicates(args.source, conn, args.dry_run)
    finally:
        conn.close()

if __name__ == '__main__':
    main()