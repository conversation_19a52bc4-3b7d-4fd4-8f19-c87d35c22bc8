#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WPS Office命令行转换功能
"""

import os
import subprocess
import tempfile
import time

def test_wps_conversion():
    """测试WPS Office的文件转换功能"""
    print("开始测试WPS Office转换功能...")
    
    # 创建测试文档内容
    test_content = """这是一个测试文档
用于验证WPS Office的转换功能

包含中文内容
包含英文内容: This is English content
包含数字: 123456
包含特殊字符: @#$%^&*()

测试完成。
"""
    
    # 测试不同的转换方法
    conversion_methods = [
        # WPS Writer 转换方法
        {
            'name': 'WPS Writer 转 TXT',
            'command': ['wps', '--headless', '--convert-to', 'txt'],
            'input_ext': '.doc',
            'output_ext': '.txt'
        },
        {
            'name': 'WPS Writer 转 PDF',
            'command': ['wps', '--headless', '--convert-to', 'pdf'],
            'input_ext': '.doc',
            'output_ext': '.pdf'
        },
        # ET 转换方法
        {
            'name': 'ET 转 TXT',
            'command': ['et', '--headless', '--convert-to', 'txt'],
            'input_ext': '.xls',
            'output_ext': '.txt'
        },
        # WPP 转换方法
        {
            'name': 'WPP 转 TXT',
            'command': ['wpp', '--headless', '--convert-to', 'txt'],
            'input_ext': '.ppt',
            'output_ext': '.txt'
        }
    ]
    
    results = []
    
    with tempfile.TemporaryDirectory() as tmpdir:
        print(f"使用临时目录: {tmpdir}")
        
        for method in conversion_methods:
            print(f"\n测试: {method['name']}")
            
            try:
                # 创建测试输入文件
                input_file = os.path.join(tmpdir, f"test{method['input_ext']}")
                
                # 对于文档文件，先创建一个简单的文本文件
                with open(input_file, 'w', encoding='utf-8') as f:
                    f.write(test_content)
                
                # 构建转换命令
                cmd = method['command'] + ['--outdir', tmpdir, input_file]
                print(f"执行命令: {' '.join(cmd)}")
                
                # 执行转换
                start_time = time.time()
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                end_time = time.time()
                
                # 检查输出文件
                base_name = os.path.splitext(os.path.basename(input_file))[0]
                output_file = os.path.join(tmpdir, f"{base_name}{method['output_ext']}")
                
                success = False
                output_content = ""
                
                if result.returncode == 0 and os.path.exists(output_file):
                    success = True
                    # 读取输出内容
                    try:
                        with open(output_file, 'r', encoding='utf-8', errors='ignore') as f:
                            output_content = f.read()
                    except:
                        with open(output_file, 'rb') as f:
                            output_content = f"[二进制文件，大小: {len(f.read())} 字节]"
                
                result_info = {
                    'method': method['name'],
                    'success': success,
                    'return_code': result.returncode,
                    'duration': end_time - start_time,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'output_exists': os.path.exists(output_file),
                    'output_content': output_content[:200] + "..." if len(output_content) > 200 else output_content
                }
                
                results.append(result_info)
                
                print(f"  返回码: {result.returncode}")
                print(f"  耗时: {end_time - start_time:.2f}秒")
                print(f"  输出文件存在: {os.path.exists(output_file)}")
                if result.stdout:
                    print(f"  标准输出: {result.stdout}")
                if result.stderr:
                    print(f"  标准错误: {result.stderr}")
                if output_content:
                    print(f"  输出内容预览: {output_content[:100]}...")
                
            except subprocess.TimeoutExpired:
                print(f"  转换超时")
                results.append({
                    'method': method['name'],
                    'success': False,
                    'error': 'timeout'
                })
            except Exception as e:
                print(f"  转换失败: {e}")
                results.append({
                    'method': method['name'],
                    'success': False,
                    'error': str(e)
                })
    
    # 总结测试结果
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    successful_methods = []
    failed_methods = []
    
    for result in results:
        if result.get('success', False):
            successful_methods.append(result['method'])
            print(f"✓ {result['method']} - 成功")
        else:
            failed_methods.append(result['method'])
            error_info = result.get('error', f"返回码: {result.get('return_code', 'unknown')}")
            print(f"✗ {result['method']} - 失败 ({error_info})")
    
    print(f"\n成功的方法数: {len(successful_methods)}")
    print(f"失败的方法数: {len(failed_methods)}")
    
    if successful_methods:
        print(f"\n推荐使用的WPS转换方法:")
        for method in successful_methods:
            print(f"  - {method}")
        return True
    else:
        print("\n所有WPS转换方法都失败了")
        return False

if __name__ == "__main__":
    success = test_wps_conversion()
    exit(0 if success else 1)
