#!/usr/bin/env python3
"""
测试修改后的图像检测功能
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('/home/<USER>/archive_pull')
from extract_utils import extract_file_content_and_meta

def test_image_detection():
    """测试图像检测功能"""
    # 测试之前失败的文件
    test_files = [
        "/data/ready_upload/f8769543088_1.pptx",  # 大的PPTX文件，可能包含图像
        "/data/ready_upload/f4988655456_1.docx",   # DOCX文件
        "/data/ready_upload/f9235898032.pptx",     # 另一个PPTX文件
        "/data/ready_upload/f2775720160_1.pptx",   # 之前成功的PPTX文件
        "/data/ready_upload/f1820358824_1.pptx"    # 另一个成功的PPTX文件
    ]
    
    print("测试修改后的图像检测功能")
    print("=" * 60)
    
    for i, filepath in enumerate(test_files, 1):
        if not os.path.exists(filepath):
            print(f"[{i}] 文件不存在: {os.path.basename(filepath)}")
            continue
            
        print(f"\n[{i}] 测试文件: {os.path.basename(filepath)}")
        print(f"文件大小: {os.path.getsize(filepath) / (1024*1024):.2f} MB")
        
        try:
            text, create_time, creator = extract_file_content_and_meta(filepath)
            
            if text and text.strip():
                print(f"✅ 提取成功")
                print(f"内容长度: {len(text)} 字符")
                print(f"内容预览: {text[:100]}...")
                if "图像" in text or "图表" in text:
                    print("🖼️  检测到图像内容描述")
            else:
                print("❌ 提取失败：无内容")
                
        except Exception as e:
            print(f"❌ 提取异常: {e}")

def test_specific_pptx():
    """专门测试PPTX文件的图像检测"""
    print("\n" + "=" * 60)
    print("专门测试PPTX文件的图像检测")
    print("=" * 60)
    
    # 选择一个大的PPTX文件进行详细测试
    filepath = "/data/ready_upload/f8769543088_1.pptx"
    
    if not os.path.exists(filepath):
        print("测试文件不存在")
        return
    
    print(f"测试文件: {os.path.basename(filepath)}")
    print(f"文件大小: {os.path.getsize(filepath) / (1024*1024):.2f} MB")
    
    try:
        from pptx import Presentation
        prs = Presentation(filepath)
        
        print(f"幻灯片数量: {len(prs.slides)}")
        
        total_shapes = 0
        text_shapes = 0
        image_shapes = 0
        
        for i, slide in enumerate(prs.slides):
            slide_shapes = len(slide.shapes)
            slide_text_shapes = 0
            slide_image_shapes = 0
            
            for shape in slide.shapes:
                total_shapes += 1
                if hasattr(shape, "text") and shape.text.strip():
                    text_shapes += 1
                    slide_text_shapes += 1
                elif hasattr(shape, "shape_type"):
                    if shape.shape_type in [13, 3, 19]:  # 图片、图表、表格
                        image_shapes += 1
                        slide_image_shapes += 1
            
            print(f"  幻灯片 {i+1}: {slide_shapes} 个形状 ({slide_text_shapes} 文本, {slide_image_shapes} 图像)")
        
        print(f"\n总计:")
        print(f"  总形状数: {total_shapes}")
        print(f"  文本形状: {text_shapes}")
        print(f"  图像形状: {image_shapes}")
        
        # 测试提取结果
        text, create_time, creator = extract_file_content_and_meta(filepath)
        print(f"\n提取结果:")
        if text:
            print(f"  内容: {text}")
        else:
            print("  无内容")
            
    except Exception as e:
        print(f"详细测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_image_detection()
    test_specific_pptx()
