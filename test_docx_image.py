#!/usr/bin/env python3
"""
专门测试DOCX文件的图像检测
"""

import os
import sys

# 添加当前目录到路径
sys.path.append('/home/<USER>/archive_pull')

def test_docx_image_detection():
    """测试DOCX文件的图像检测"""
    filepath = "/data/ready_upload/f4988655456_1.docx"
    
    if not os.path.exists(filepath):
        print("测试文件不存在")
        return
    
    print(f"测试DOCX文件: {os.path.basename(filepath)}")
    print(f"文件大小: {os.path.getsize(filepath)} 字节")
    
    try:
        from docx import Document
        doc = Document(filepath)
        
        print(f"段落数量: {len(doc.paragraphs)}")
        
        # 检查段落文本
        text_content = []
        for i, para in enumerate(doc.paragraphs):
            if para.text.strip():
                text_content.append(para.text.strip())
                print(f"  段落 {i+1}: {para.text[:50]}...")
        
        if not text_content:
            print("  没有找到文本内容")
        
        # 检查图像
        print("\n检查图像:")
        image_count = 0
        
        # 方法1：检查关系
        print("  方法1 - 检查文档关系:")
        for rel in doc.part.rels.values():
            print(f"    关系: {rel.target_ref}")
            if "image" in rel.target_ref:
                image_count += 1
                print(f"    找到图像: {rel.target_ref}")
        
        # 方法2：检查运行元素
        print("  方法2 - 检查运行元素:")
        for para in doc.paragraphs:
            for run in para.runs:
                if run._element.xpath('.//a:blip'):
                    image_count += 1
                    print(f"    找到图像元素")
        
        # 方法3：检查内嵌形状
        print("  方法3 - 检查内嵌形状:")
        try:
            from docx.document import Document as DocxDocument
            from docx.oxml.ns import qn
            
            # 检查所有图像元素
            blips = doc._element.xpath('.//a:blip', namespaces=doc._element.nsmap)
            print(f"    找到 {len(blips)} 个图像元素")
            image_count += len(blips)
            
        except Exception as e:
            print(f"    方法3失败: {e}")
        
        print(f"\n总图像数: {image_count}")
        
        # 测试我们的提取函数
        print("\n测试提取函数:")
        from extract_utils import extract_docx_content_and_meta
        text, create_time, creator = extract_docx_content_and_meta(filepath)
        
        if text:
            print(f"  提取结果: {text}")
        else:
            print("  提取结果: 无内容")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_docx_image_detection()
